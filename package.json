{"name": "snap-dashboard", "version": "1.0.0", "description": "Snap Dashboard for Merch on Demand - A comprehensive analytics and management platform", "main": "snapapp.js", "scripts": {"start": "http-server -p 8080 -c-1", "dev": "http-server -p 8080 -c-1 --cors", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "security:audit": "npm audit && eslint . --ext .js --config .eslintrc.security.js", "security:check": "npm audit --audit-level moderate", "test": "echo \"Error: no test specified\" && exit 1", "build": "echo \"No build process required for vanilla JS app\"", "validate": "npm run lint && npm run security:check"}, "keywords": ["dashboard", "analytics", "merch", "print-on-demand", "charts", "visualization"], "author": "Snap Dashboard Team", "license": "MIT", "dependencies": {"dompurify": "^3.0.5"}, "devDependencies": {"eslint": "^8.57.0", "eslint-plugin-security": "^1.7.1", "eslint-plugin-no-unsanitized": "^4.0.2", "http-server": "^14.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/snap-dashboard.git"}, "bugs": {"url": "https://github.com/your-org/snap-dashboard/issues"}, "homepage": "https://github.com/your-org/snap-dashboard#readme", "browserslist": ["> 1%", "last 2 versions", "not dead"], "eslintConfig": {"extends": ["./.eslintrc.js"]}, "security": {"advisories": {"ignore": []}}}