/**
 * Content Class Manager
 * Replaces CSS :contains() pseudo-classes with JavaScript-based class assignment
 * for better browser compatibility and maintainability
 */

class ContentClassManager {
  /**
   * Initialize a new ContentClassManager instance
   * @constructor
   */
  constructor() {
    // Core state management
    this.initialized = false;
    
    // Observer management
    this.observers = new Map();
    
    // Debouncing and timing
    this.reprocessTimeout = null;
    
    // Event handler references for cleanup
    this.domReadyHandler = null;
    
    // Mutation observer configuration
    this.observerConfig = {
      childList: true,
      subtree: true
    };
  }

  /**
   * Initialize the content class manager
   * Sets up content-based classes and mutation observer for dynamic content
   */
  init() {
    if (this.initialized) return;
    
    this.setupContentBasedClasses();
    this.setupMutationObserver();
    this.initialized = true;
  }

  /**
   * Set up content-based class assignments for existing elements
   * Processes all relevant elements currently in the DOM
   */
  setupContentBasedClasses() {
    // Handle canceled units badges
    this.processCanceledUnitsBadges();
    
    // <PERSON>le returned units badges
    this.processReturnedUnitsBadges();
    
    // Handle listing ad rows with zero values
    this.processListingAdRows();
  }

  /**
   * Process a single canceled units badge and add appropriate classes
   * @param {Element} badge - The badge element to process
   * @param {boolean} [addClass=true] - Whether to add the class (false to remove)
   */
  processSingleCanceledUnitsBadge(badge, addClass = true) {
    const lastSpan = badge.querySelector('span:last-child');
    if (lastSpan) {
      const text = lastSpan.textContent.trim();
      const dataValue = lastSpan.getAttribute('data-value');
      
      // Check if the badge has a zero value (either in data-value attribute or text content)
      // This is important for styling badges that represent no canceled units
      if (dataValue === '0' || text === '0') {
        if (addClass) {
          badge.classList.add('has-zero-value');
        }
      } else {
        // Remove the class if it exists, ensuring clean state
        badge.classList.remove('has-zero-value');
      }
    }
  }

  /**
   * Process a single returned units badge and add appropriate classes
   * @param {Element} badge - The badge element to process
   * @param {boolean} [addClass=true] - Whether to add the class (false to remove)
   */
  processSingleReturnedUnitsBadge(badge, addClass = true) {
    const lastSpan = badge.querySelector('span:last-child');
    if (lastSpan) {
      const text = lastSpan.textContent.trim();
      const dataValue = lastSpan.getAttribute('data-value');
      
      // Check for both positive and negative zero values
      // Negative zero can occur in some data formats and should be treated as zero
      if (dataValue === '0' || dataValue === '-0' || text === '0' || text === '-0') {
        if (addClass) {
          badge.classList.add('has-zero-value');
        }
      } else {
        // Remove the class if it exists, ensuring clean state
        badge.classList.remove('has-zero-value');
      }
    }
  }

  /**
   * Process a single listing ad row and add appropriate classes for zero values
   * @param {Element} row - The row element to process
   * @param {boolean} [addClass=true] - Whether to add the class (false to remove)
   */
  processSingleListingAdRow(row, addClass = true) {
    const label = row.querySelector('.listing-ad-label');
    if (label) {
      const text = label.textContent.trim();
      
      // Check for various zero value patterns across different currencies and formats
      // This comprehensive check ensures we catch all variations of zero ad spend
      const hasZeroValue = 
        text.includes('$0') ||
        text.includes('€0') ||
        text.includes('£0') ||
        text.includes('¥0') ||
        text.includes('(0)') ||
        text.includes('0.0') ||
        text.includes('0,0');
      
      if (hasZeroValue) {
        if (addClass) {
          row.classList.add('has-zero-ad-spend');
        }
      } else {
        // Remove the class if it exists, ensuring clean state
        row.classList.remove('has-zero-ad-spend');
      }
    }
  }

  /**
   * Process canceled units badges and add appropriate classes
   * Batch processes all canceled units badges in the DOM
   */
  processCanceledUnitsBadges() {
    const badges = document.querySelectorAll('.canceled-units-badge');
    badges.forEach(badge => {
      this.processSingleCanceledUnitsBadge(badge);
    });
  }

  /**
   * Process returned units badges and add appropriate classes
   * Batch processes all returned units badges in the DOM
   */
  processReturnedUnitsBadges() {
    const badges = document.querySelectorAll('.returned-units-badge');
    badges.forEach(badge => {
      this.processSingleReturnedUnitsBadge(badge);
    });
  }

  /**
   * Process listing ad rows and add appropriate classes for zero values
   * Batch processes all listing ad rows in the DOM
   */
  processListingAdRows() {
    const rows = document.querySelectorAll('.listing-ad-row');
    rows.forEach(row => {
      this.processSingleListingAdRow(row);
    });
  }

  /**
   * Set up mutation observer to handle dynamically added content
   * Monitors DOM changes and reprocesses relevant elements when new content is added
   */
  setupMutationObserver() {
    // Create mutation observer callback that handles DOM changes
    const mutationCallback = (mutations) => {
      try {
        let shouldReprocess = false;
        
        // Analyze each mutation to determine if reprocessing is needed
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Check if any of the added nodes are relevant elements we need to process
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Check if the added node itself is a relevant element
                if (node.classList?.contains('canceled-units-badge') ||
                    node.classList?.contains('returned-units-badge') ||
                    node.classList?.contains('listing-ad-row') ||
                    // Check if the added node contains any relevant child elements
                    node.querySelector('.canceled-units-badge, .returned-units-badge, .listing-ad-row')) {
                  shouldReprocess = true;
                }
              }
            });
          }
        });
        
        // If relevant elements were added, schedule reprocessing with debouncing
        if (shouldReprocess) {
          // Debounce the reprocessing to avoid excessive calls when multiple elements
          // are added in quick succession (e.g., during page load or AJAX updates)
          clearTimeout(this.reprocessTimeout);
          this.reprocessTimeout = setTimeout(() => {
            this.setupContentBasedClasses();
          }, 100);
        }
      } catch (error) {
        // Log error details to aid debugging while preventing observer from stopping
        // This ensures the mutation observer continues to function even if individual
        // mutations cause errors
        if (window.SnapLogger) {
          window.SnapLogger.error('ContentClassManager: Error in mutation callback:', {
            error: error.message,
            stack: error.stack,
            mutationsCount: mutations.length
          });
        } else {
          console.error('ContentClassManager: Error in mutation callback:', {
            error: error.message,
            stack: error.stack,
            mutationsCount: mutations.length
          });
        }
      }
    };

    // Create and configure the mutation observer
    const observer = new MutationObserver(mutationCallback);
    
    // Start observing the entire document body for changes
    // This ensures we catch all dynamically added content regardless of location
    observer.observe(document.body, this.observerConfig);

    // Store the observer reference for proper cleanup
    this.observers.set('main', observer);
  }

  /**
   * Clean up observers and handlers when the manager is destroyed
   * Ensures proper memory cleanup and prevents memory leaks
   */
  destroy() {
    // Clear any pending timeouts to prevent memory leaks
    if (this.reprocessTimeout) {
      clearTimeout(this.reprocessTimeout);
      this.reprocessTimeout = null;
    }
    
    // Disconnect all mutation observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    
    // Remove event listeners if they exist
    if (this.domReadyHandler) {
      document.removeEventListener('DOMContentLoaded', this.domReadyHandler);
      this.domReadyHandler = null;
    }
    
    // Reset initialization state
    this.initialized = false;
  }
}

/**
 * Factory function to create and initialize a ContentClassManager instance
 * Only initializes when running in a browser environment to prevent SSR issues
 * @returns {ContentClassManager|null} The initialized manager instance or null if not in browser
 */
export function createContentClassManager() {
  // Check if we're in a browser environment with DOM access
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return null;
  }
  
  const manager = new ContentClassManager();
  
  // Set up DOM ready handler for initialization
  if (document.readyState === 'loading') {
    manager.domReadyHandler = () => {
      manager.init();
    };
    document.addEventListener('DOMContentLoaded', manager.domReadyHandler);
  } else {
    // DOM is already ready, initialize immediately
    manager.init();
  }
  
  return manager;
}

/**
 * Get or create the global content class manager instance
 * Provides a singleton pattern while avoiding SSR issues
 * @returns {ContentClassManager|null} The global manager instance or null if not in browser
 */
export function getContentClassManager() {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return null;
  }
  
  // Create global instance if it doesn't exist
  if (!window.contentClassManager) {
    window.contentClassManager = createContentClassManager();
  }
  
  return window.contentClassManager;
}

// Export the class for direct usage
export default ContentClassManager;
