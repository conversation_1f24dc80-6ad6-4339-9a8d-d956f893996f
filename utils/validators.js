/**
 * Input Validation Utility
 * Provides comprehensive validation for different input types
 */

/**
 * Validation result structure
 */
class ValidationResult {
    constructor(isValid, value = null, errors = []) {
        this.isValid = isValid;
        this.value = value;
        this.errors = errors;
    }
    
    addError(error) {
        this.errors.push(error);
        this.isValid = false;
        return this;
    }
}

/**
 * Validation configuration constants
 */
const VALIDATION_LIMITS = {
    SEARCH_INPUT: {
        MIN_LENGTH: 1,
        MAX_LENGTH: 500,
        ALLOWED_CHARS: /^[a-zA-Z0-9\s\-_.,!?'"()[\]{}@#$%&*+=<>\/\\|~`^:;]*$/
    },
    FILE_INPUT: {
        MAX_SIZE: 50 * 1024 * 1024, // 50MB
        ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
        ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
    },
    CHART_DATA: {
        MAX_JSON_SIZE: 1024 * 1024, // 1MB
        MAX_ARRAY_LENGTH: 10000,
        MAX_OBJECT_DEPTH: 10
    },
    MARKETPLACE: {
        ALLOWED_VALUES: ['all', 'amazon', 'etsy', 'ebay', 'shopify', 'woocommerce', 'bigcommerce']
    },
    GENERAL: {
        MAX_STRING_LENGTH: 10000,
        MAX_NUMBER_VALUE: Number.MAX_SAFE_INTEGER,
        MIN_NUMBER_VALUE: Number.MIN_SAFE_INTEGER
    }
};

/**
 * Validate search input with length limits and pattern matching
 * @param {string} input - Search query to validate
 * @param {Object} options - Additional validation options
 * @returns {ValidationResult} Validation result
 */
export function validateSearchInput(input, options = {}) {
    const result = new ValidationResult(true, input);
    
    // Check if input is string
    if (typeof input !== 'string') {
        return result.addError('Search input must be a string');
    }
    
    // Check length limits
    const minLength = options.minLength || VALIDATION_LIMITS.SEARCH_INPUT.MIN_LENGTH;
    const maxLength = options.maxLength || VALIDATION_LIMITS.SEARCH_INPUT.MAX_LENGTH;
    
    if (input.length < minLength) {
        return result.addError(`Search input must be at least ${minLength} characters`);
    }
    
    if (input.length > maxLength) {
        return result.addError(`Search input must not exceed ${maxLength} characters`);
    }
    
    // Check for allowed characters
    if (!VALIDATION_LIMITS.SEARCH_INPUT.ALLOWED_CHARS.test(input)) {
        return result.addError('Search input contains invalid characters');
    }
    
    // Check for potential XSS patterns
    const xssPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /<iframe/i,
        /data:.*base64/i
    ];
    
    for (const pattern of xssPatterns) {
        if (pattern.test(input)) {
            return result.addError('Search input contains potentially dangerous content');
        }
    }
    
    // Sanitize and return cleaned value
    result.value = input.trim();
    return result;
}

/**
 * Validate file input with type and size restrictions
 * @param {File} file - File object to validate
 * @param {Object} options - Additional validation options
 * @returns {ValidationResult} Validation result
 */
export function validateFileInput(file, options = {}) {
    const result = new ValidationResult(true, file);
    
    // Check if file exists
    if (!file) {
        return result.addError('No file provided');
    }
    
    // Check if it's a File object
    if (!(file instanceof File)) {
        return result.addError('Invalid file object');
    }
    
    // Check file size
    const maxSize = options.maxSize || VALIDATION_LIMITS.FILE_INPUT.MAX_SIZE;
    if (file.size > maxSize) {
        return result.addError(`File size exceeds maximum limit of ${Math.round(maxSize / 1024 / 1024)}MB`);
    }
    
    // Check file type
    const allowedTypes = options.allowedTypes || VALIDATION_LIMITS.FILE_INPUT.ALLOWED_TYPES;
    if (!allowedTypes.includes(file.type)) {
        return result.addError(`File type ${file.type} is not allowed`);
    }
    
    // Check file extension
    const allowedExtensions = options.allowedExtensions || VALIDATION_LIMITS.FILE_INPUT.ALLOWED_EXTENSIONS;
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    if (!allowedExtensions.includes(fileExtension)) {
        return result.addError(`File extension ${fileExtension} is not allowed`);
    }
    
    // Check filename for path traversal
    if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
        return result.addError('Invalid filename - path traversal detected');
    }
    
    // Check for suspicious filenames
    const suspiciousPatterns = [
        /\.php$/i,
        /\.asp$/i,
        /\.jsp$/i,
        /\.exe$/i,
        /\.bat$/i,
        /\.cmd$/i,
        /\.scr$/i
    ];
    
    for (const pattern of suspiciousPatterns) {
        if (pattern.test(file.name)) {
            return result.addError('Suspicious file type detected');
        }
    }
    
    return result;
}

/**
 * Validate chart data editor inputs
 * @param {string} jsonString - JSON string to validate
 * @param {Object} options - Additional validation options
 * @returns {ValidationResult} Validation result
 */
export function validateChartData(jsonString, options = {}) {
    const result = new ValidationResult(true);
    
    // Check if input is string
    if (typeof jsonString !== 'string') {
        return result.addError('Chart data must be a string');
    }
    
    // Check size limits
    const maxSize = options.maxSize || VALIDATION_LIMITS.CHART_DATA.MAX_JSON_SIZE;
    if (jsonString.length > maxSize) {
        return result.addError(`Chart data exceeds maximum size of ${Math.round(maxSize / 1024)}KB`);
    }
    
    // Try to parse JSON
    let parsedData;
    try {
        parsedData = JSON.parse(jsonString);
    } catch (error) {
        return result.addError('Invalid JSON format: ' + error.message);
    }
    
    // Validate parsed data structure
    const validation = validateObjectDepth(parsedData, options.maxDepth || VALIDATION_LIMITS.CHART_DATA.MAX_OBJECT_DEPTH);
    if (!validation.isValid) {
        return result.addError(validation.errors[0]);
    }
    
    // Check for dangerous properties
    const dangerousKeys = ['__proto__', 'constructor', 'prototype', 'eval', 'function'];
    if (containsDangerousKeys(parsedData, dangerousKeys)) {
        return result.addError('Chart data contains dangerous properties');
    }
    
    result.value = parsedData;
    return result;
}

/**
 * Validate marketplace selection dropdown values
 * @param {string} marketplace - Marketplace value to validate
 * @returns {ValidationResult} Validation result
 */
export function validateMarketplaceSelection(marketplace) {
    const result = new ValidationResult(true, marketplace);
    
    // Check if input is string
    if (typeof marketplace !== 'string') {
        return result.addError('Marketplace selection must be a string');
    }
    
    // Check against allowed values
    const allowedValues = VALIDATION_LIMITS.MARKETPLACE.ALLOWED_VALUES;
    if (!allowedValues.includes(marketplace.toLowerCase())) {
        return result.addError(`Invalid marketplace selection: ${marketplace}`);
    }
    
    result.value = marketplace.toLowerCase();
    return result;
}

/**
 * Validate numeric inputs
 * @param {any} value - Value to validate as number
 * @param {Object} options - Validation options (min, max, integer)
 * @returns {ValidationResult} Validation result
 */
export function validateNumericInput(value, options = {}) {
    const result = new ValidationResult(true);
    
    // Convert to number
    const numValue = Number(value);
    
    // Check if it's a valid number
    if (isNaN(numValue) || !isFinite(numValue)) {
        return result.addError('Value must be a valid number');
    }
    
    // Check range limits
    const min = options.min !== undefined ? options.min : VALIDATION_LIMITS.GENERAL.MIN_NUMBER_VALUE;
    const max = options.max !== undefined ? options.max : VALIDATION_LIMITS.GENERAL.MAX_NUMBER_VALUE;
    
    if (numValue < min) {
        return result.addError(`Value must be at least ${min}`);
    }
    
    if (numValue > max) {
        return result.addError(`Value must not exceed ${max}`);
    }
    
    // Check if integer is required
    if (options.integer && !Number.isInteger(numValue)) {
        return result.addError('Value must be an integer');
    }
    
    result.value = numValue;
    return result;
}

/**
 * Validate string length and content
 * @param {string} str - String to validate
 * @param {Object} options - Validation options
 * @returns {ValidationResult} Validation result
 */
export function validateStringInput(str, options = {}) {
    const result = new ValidationResult(true, str);
    
    // Check if input is string
    if (typeof str !== 'string') {
        return result.addError('Input must be a string');
    }
    
    // Check length limits
    const maxLength = options.maxLength || VALIDATION_LIMITS.GENERAL.MAX_STRING_LENGTH;
    if (str.length > maxLength) {
        return result.addError(`String exceeds maximum length of ${maxLength} characters`);
    }
    
    // Check minimum length if specified
    if (options.minLength && str.length < options.minLength) {
        return result.addError(`String must be at least ${options.minLength} characters`);
    }
    
    // Check pattern if specified
    if (options.pattern && !options.pattern.test(str)) {
        return result.addError('String does not match required pattern');
    }
    
    return result;
}

/**
 * Helper function to validate object depth
 * @param {any} obj - Object to validate
 * @param {number} maxDepth - Maximum allowed depth
 * @param {number} currentDepth - Current depth (internal)
 * @returns {ValidationResult} Validation result
 */
function validateObjectDepth(obj, maxDepth, currentDepth = 0) {
    const result = new ValidationResult(true);
    
    if (currentDepth > maxDepth) {
        return result.addError(`Object depth exceeds maximum of ${maxDepth}`);
    }
    
    if (obj && typeof obj === 'object') {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const validation = validateObjectDepth(obj[key], maxDepth, currentDepth + 1);
                if (!validation.isValid) {
                    return validation;
                }
            }
        }
    }
    
    return result;
}

/**
 * Helper function to check for dangerous object keys
 * @param {any} obj - Object to check
 * @param {Array} dangerousKeys - Array of dangerous key names
 * @returns {boolean} True if dangerous keys found
 */
function containsDangerousKeys(obj, dangerousKeys) {
    if (!obj || typeof obj !== 'object') {
        return false;
    }
    
    for (const key in obj) {
        if (dangerousKeys.includes(key)) {
            return true;
        }
        
        if (typeof obj[key] === 'object' && containsDangerousKeys(obj[key], dangerousKeys)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Sanitize regex pattern to prevent ReDoS attacks
 * @param {string} pattern - Regex pattern to sanitize
 * @returns {string} Sanitized pattern
 */
export function sanitizeRegexPattern(pattern) {
    if (typeof pattern !== 'string') {
        return '';
    }
    
    // Remove potentially dangerous regex constructs
    // This is a basic implementation - in production, use a proper regex sanitizer
    return pattern
        .replace(/\(\?\=/g, '') // Remove positive lookahead
        .replace(/\(\?\!/g, '') // Remove negative lookahead
        .replace(/\(\?\<\=/g, '') // Remove positive lookbehind
        .replace(/\(\?\<\!/g, '') // Remove negative lookbehind
        .replace(/\(\?\>/g, '') // Remove atomic groups
        .replace(/\*\+/g, '*') // Remove possessive quantifiers
        .replace(/\+\+/g, '+')
        .replace(/\?\+/g, '?')
        .replace(/\{\d+,\d*\}\+/g, '') // Remove possessive quantifiers with ranges
        .substring(0, 100); // Limit pattern length
}

// Export validation limits for external use
export { VALIDATION_LIMITS, ValidationResult };
