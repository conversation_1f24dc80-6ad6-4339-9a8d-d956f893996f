/**
 * Settings Page Component
 * UI for managing all user preferences and massive scale configurations
 * Integrates with SettingsManager for persistence
 */

class SettingsComponent {
    constructor(container, settingsManager) {
        this.container = container;
        this.settingsManager = settingsManager;
        this.activeTab = 'general';
        this.unsavedChanges = false;
        
        // Bind methods
        this.handleTabChange = this.handleTabChange.bind(this);
        this.handleSettingChange = this.handleSettingChange.bind(this);
        this.handleSave = this.handleSave.bind(this);
        this.handleReset = this.handleReset.bind(this);
        this.handleExport = this.handleExport.bind(this);
        this.handleImport = this.handleImport.bind(this);
        
        this.init();
    }

    /**
     * Initialize the settings component
     */
    init() {
        this.createDOM();
        this.attachEventListeners();
        this.loadCurrentSettings();
        this.updateDiagnostics();
    }

    /**
     * Create DOM structure for settings page
     */
    createDOM() {
        this.container.innerHTML = `
            <div class="settings-page">
                <div class="settings-header">
                    <h1>Settings</h1>
                    <div class="settings-actions">
                        <button class="btn-export">Export Settings</button>
                        <button class="btn-import">Import Settings</button>
                        <button class="btn-reset">Reset to Defaults</button>
                        <button class="btn-save" disabled>Save Changes</button>
                    </div>
                </div>
                
                <div class="settings-content">
                    <div class="settings-tabs">
                        <button class="tab-button active" data-tab="general">General</button>
                        <button class="tab-button" data-tab="performance">Performance</button>
                        <button class="tab-button" data-tab="data">Data Management</button>
                        <button class="tab-button" data-tab="ui">User Interface</button>
                        <button class="tab-button" data-tab="notifications">Notifications</button>
                        <button class="tab-button" data-tab="advanced">Advanced</button>
                        <button class="tab-button" data-tab="diagnostics">Diagnostics</button>
                    </div>
                    
                    <div class="settings-panels">
                        <!-- General Settings -->
                        <div class="settings-panel active" data-panel="general">
                            <h2>General Settings</h2>
                            
                            <div class="setting-group">
                                <h3>Data Synchronization</h3>
                                <div class="setting-item">
                                    <label>Fetch Frequency (minutes)</label>
                                    <input type="number" name="dataSync.fetchFrequency" min="1" max="60" />
                                    <span class="setting-help">How often to check for new data</span>
                                </div>
                                <div class="setting-item">
                                    <label>Enable Real-time Updates</label>
                                    <input type="checkbox" name="dataSync.enableRealTime" />
                                    <span class="setting-help">Automatically update data in real-time</span>
                                </div>
                                <div class="setting-item">
                                    <label>Auto Backup</label>
                                    <input type="checkbox" name="dataSync.autoBackup" />
                                    <span class="setting-help">Automatically backup data daily</span>
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Marketplace Preferences</h3>
                                <div class="setting-item">
                                    <label>Default Marketplace</label>
                                    <select name="marketplace.defaultMarketplace">
                                        <option value="all">All Marketplaces</option>
                                        <option value="amazon.com">Amazon.com</option>
                                        <option value="amazon.co.uk">Amazon.co.uk</option>
                                        <option value="amazon.de">Amazon.de</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label>Hide Inactive Marketplaces</label>
                                    <input type="checkbox" name="marketplace.hideInactiveMarketplaces" />
                                </div>
                            </div>
                        </div>
                        
                        <!-- Performance Settings -->
                        <div class="settings-panel" data-panel="performance">
                            <h2>Performance Settings</h2>
                            
                            <div class="setting-group">
                                <h3>Memory Management</h3>
                                <div class="setting-item">
                                    <label>Maximum Memory Usage (MB)</label>
                                    <input type="number" name="memory.maxMemoryUsage" min="128" max="2048" step="64" />
                                    <span class="setting-help">Maximum memory for the application</span>
                                </div>
                                <div class="setting-item">
                                    <label>Cleanup Threshold (%)</label>
                                    <input type="range" name="memory.cleanupThreshold" min="0.5" max="1" step="0.05" />
                                    <span class="threshold-value">80%</span>
                                </div>
                                <div class="setting-item">
                                    <label>Aggressive Cleanup</label>
                                    <input type="checkbox" name="memory.aggressiveCleanup" />
                                    <span class="setting-help">More aggressive memory cleanup for low-memory devices</span>
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Virtual List Performance</h3>
                                <div class="setting-item">
                                    <label>Item Height (px)</label>
                                    <input type="number" name="virtualList.itemHeight" min="40" max="200" />
                                </div>
                                <div class="setting-item">
                                    <label>Page Size</label>
                                    <input type="number" name="virtualList.pageSize" min="10" max="1000" />
                                    <span class="setting-help">Number of items to load per page</span>
                                </div>
                                <div class="setting-item">
                                    <label>Buffer Size</label>
                                    <input type="number" name="virtualList.bufferSize" min="5" max="50" />
                                    <span class="setting-help">Extra items to render outside viewport</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Data Management Settings -->
                        <div class="settings-panel" data-panel="data">
                            <h2>Data Management</h2>
                            
                            <div class="setting-group">
                                <h3>Data Retention</h3>
                                <div class="setting-item">
                                    <label>Product Data Retention (days)</label>
                                    <input type="number" name="dataRetention.maxProductAge" min="1" max="365" />
                                </div>
                                <div class="setting-item">
                                    <label>Sales Data Retention (days)</label>
                                    <input type="number" name="dataRetention.maxSalesAge" min="7" max="1095" />
                                </div>
                                <div class="setting-item">
                                    <label>Enable Auto Cleanup</label>
                                    <input type="checkbox" name="dataRetention.enableAutoCleanup" />
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Export Settings</h3>
                                <div class="setting-item">
                                    <label>Default Export Format</label>
                                    <select name="export.defaultFormat">
                                        <option value="json">JSON</option>
                                        <option value="csv">CSV</option>
                                        <option value="xlsx">Excel</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label>Compress Exports</label>
                                    <input type="checkbox" name="export.compressExports" />
                                </div>
                            </div>
                        </div>
                        
                        <!-- UI Settings -->
                        <div class="settings-panel" data-panel="ui">
                            <h2>User Interface</h2>
                            
                            <div class="setting-group">
                                <h3>Appearance</h3>
                                <div class="setting-item">
                                    <label>Theme</label>
                                    <select name="ui.theme">
                                        <option value="light">Light</option>
                                        <option value="dark">Dark</option>
                                        <option value="auto">Auto (System)</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label>Compact Mode</label>
                                    <input type="checkbox" name="ui.compactMode" />
                                    <span class="setting-help">Reduce spacing for more content</span>
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Localization</h3>
                                <div class="setting-item">
                                    <label>Date Format</label>
                                    <select name="ui.dateFormat">
                                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label>Currency</label>
                                    <select name="ui.currency">
                                        <option value="USD">USD ($)</option>
                                        <option value="EUR">EUR (€)</option>
                                        <option value="GBP">GBP (£)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notifications Settings -->
                        <div class="settings-panel" data-panel="notifications">
                            <h2>Notifications</h2>
                            
                            <div class="setting-group">
                                <h3>Alert Types</h3>
                                <div class="setting-item">
                                    <label>Desktop Notifications</label>
                                    <input type="checkbox" name="notifications.enableDesktop" />
                                </div>
                                <div class="setting-item">
                                    <label>Sound Alerts</label>
                                    <input type="checkbox" name="notifications.enableSound" />
                                </div>
                                <div class="setting-item">
                                    <label>Performance Alerts</label>
                                    <input type="checkbox" name="notifications.enablePerformanceAlerts" />
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Alert Thresholds</h3>
                                <div class="setting-item">
                                    <label>Memory Usage Alert (%)</label>
                                    <input type="range" name="notifications.alertThresholds.memoryUsage" min="0.5" max="1" step="0.05" />
                                    <span class="threshold-value">85%</span>
                                </div>
                                <div class="setting-item">
                                    <label>Slow Query Alert (seconds)</label>
                                    <input type="number" name="notifications.alertThresholds.queryTime" min="1" max="30" />
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced Settings -->
                        <div class="settings-panel" data-panel="advanced">
                            <h2>Advanced Settings</h2>
                            
                            <div class="setting-group">
                                <h3>Developer Options</h3>
                                <div class="setting-item">
                                    <label>Enable Debug Mode</label>
                                    <input type="checkbox" name="advanced.enableDebugMode" />
                                    <span class="setting-help">Show debug information in console</span>
                                </div>
                                <div class="setting-item">
                                    <label>Verbose Logging</label>
                                    <input type="checkbox" name="advanced.enableVerboseLogging" />
                                </div>
                                <div class="setting-item">
                                    <label>Performance Profiling</label>
                                    <input type="checkbox" name="advanced.enablePerformanceProfiling" />
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Experimental Features</h3>
                                <div class="setting-item">
                                    <label>Enable Experimental Features</label>
                                    <input type="checkbox" name="advanced.enableExperimentalFeatures" />
                                    <span class="setting-help">⚠️ May cause instability</span>
                                </div>
                                <div class="setting-item">
                                    <label>Max Concurrent Queries</label>
                                    <input type="number" name="advanced.maxConcurrentQueries" min="1" max="20" />
                                </div>
                            </div>
                        </div>
                        
                        <!-- Diagnostics Panel -->
                        <div class="settings-panel" data-panel="diagnostics">
                            <h2>System Diagnostics</h2>
                            
                            <div class="diagnostic-section">
                                <h3>Current Status</h3>
                                <div class="diagnostic-grid">
                                    <div class="diagnostic-item">
                                        <label>Memory Usage</label>
                                        <span class="diagnostic-value" id="current-memory">--</span>
                                    </div>
                                    <div class="diagnostic-item">
                                        <label>IndexedDB Size</label>
                                        <span class="diagnostic-value" id="db-size">--</span>
                                    </div>
                                    <div class="diagnostic-item">
                                        <label>Products Loaded</label>
                                        <span class="diagnostic-value" id="products-count">--</span>
                                    </div>
                                    <div class="diagnostic-item">
                                        <label>Sales Records</label>
                                        <span class="diagnostic-value" id="sales-count">--</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="diagnostic-section">
                                <h3>Performance Metrics</h3>
                                <div class="diagnostic-grid">
                                    <div class="diagnostic-item">
                                        <label>Average Query Time</label>
                                        <span class="diagnostic-value" id="avg-query-time">--</span>
                                    </div>
                                    <div class="diagnostic-item">
                                        <label>Cache Hit Rate</label>
                                        <span class="diagnostic-value" id="cache-hit-rate">--</span>
                                    </div>
                                    <div class="diagnostic-item">
                                        <label>Error Rate</label>
                                        <span class="diagnostic-value" id="error-rate">--</span>
                                    </div>
                                    <div class="diagnostic-item">
                                        <label>Uptime</label>
                                        <span class="diagnostic-value" id="uptime">--</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="diagnostic-actions">
                                <button class="btn-refresh-diagnostics">Refresh</button>
                                <button class="btn-clear-cache">Clear Cache</button>
                                <button class="btn-run-cleanup">Run Cleanup</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="settings-footer">
                    <div class="unsaved-indicator" style="display: none;">
                        <span>⚠️ You have unsaved changes</span>
                    </div>
                </div>
            </div>
        `;

        // Get DOM references
        this.tabButtons = this.container.querySelectorAll('.tab-button');
        this.panels = this.container.querySelectorAll('.settings-panel');
        this.saveButton = this.container.querySelector('.btn-save');
        this.resetButton = this.container.querySelector('.btn-reset');
        this.exportButton = this.container.querySelector('.btn-export');
        this.importButton = this.container.querySelector('.btn-import');
        this.unsavedIndicator = this.container.querySelector('.unsaved-indicator');
        this.settingInputs = this.container.querySelectorAll('input, select');
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Tab navigation
        this.tabButtons.forEach(button => {
            button.addEventListener('click', this.handleTabChange);
        });

        // Setting changes
        this.settingInputs.forEach(input => {
            input.addEventListener('change', this.handleSettingChange);
            input.addEventListener('input', this.handleSettingChange);
        });

        // Action buttons
        this.saveButton.addEventListener('click', this.handleSave);
        this.resetButton.addEventListener('click', this.handleReset);
        this.exportButton.addEventListener('click', this.handleExport);
        this.importButton.addEventListener('click', this.handleImport);

        // Diagnostic actions
        const refreshBtn = this.container.querySelector('.btn-refresh-diagnostics');
        const clearCacheBtn = this.container.querySelector('.btn-clear-cache');
        const cleanupBtn = this.container.querySelector('.btn-run-cleanup');

        if (refreshBtn) refreshBtn.addEventListener('click', () => this.updateDiagnostics());
        if (clearCacheBtn) clearCacheBtn.addEventListener('click', () => this.clearCache());
        if (cleanupBtn) cleanupBtn.addEventListener('click', () => this.runCleanup());

        // Range input updates
        this.container.querySelectorAll('input[type="range"]').forEach(range => {
            range.addEventListener('input', (e) => {
                const valueSpan = e.target.parentElement.querySelector('.threshold-value');
                if (valueSpan) {
                    valueSpan.textContent = Math.round(e.target.value * 100) + '%';
                }
            });
        });
    }

    /**
     * Load current settings into form
     */
    loadCurrentSettings() {
        this.settingInputs.forEach(input => {
            const settingPath = input.name;
            if (!settingPath) return;

            const value = this.settingsManager.get(settingPath);
            
            if (input.type === 'checkbox') {
                input.checked = Boolean(value);
            } else if (input.type === 'range') {
                input.value = value;
                const valueSpan = input.parentElement.querySelector('.threshold-value');
                if (valueSpan) {
                    valueSpan.textContent = Math.round(value * 100) + '%';
                }
            } else if (settingPath.includes('Age')) {
                // Convert milliseconds to days for display
                input.value = Math.floor(value / 86400000);
            } else if (settingPath === 'dataSync.fetchFrequency') {
                // Convert milliseconds to minutes for display
                input.value = Math.floor(value / 60000);
            } else {
                input.value = value;
            }
        });
    }

    /**
     * Handle tab change
     */
    handleTabChange(event) {
        const tabName = event.target.dataset.tab;
        
        // Update active tab
        this.tabButtons.forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
        
        // Update active panel
        this.panels.forEach(panel => panel.classList.remove('active'));
        const targetPanel = this.container.querySelector(`[data-panel="${tabName}"]`);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }
        
        this.activeTab = tabName;
        
        // Update diagnostics if switching to diagnostics tab
        if (tabName === 'diagnostics') {
            this.updateDiagnostics();
        }
    }

    /**
     * Handle setting change
     */
    handleSettingChange(event) {
        this.unsavedChanges = true;
        this.saveButton.disabled = false;
        this.unsavedIndicator.style.display = 'block';
    }

    /**
     * Handle save button click
     */
    handleSave() {
        const updates = {};
        
        this.settingInputs.forEach(input => {
            const settingPath = input.name;
            if (!settingPath) return;

            let value;
            
            if (input.type === 'checkbox') {
                value = input.checked;
            } else if (input.type === 'number') {
                value = parseFloat(input.value);
                
                // Convert display values back to internal format
                if (settingPath.includes('Age')) {
                    value = value * 86400000; // Days to milliseconds
                } else if (settingPath === 'dataSync.fetchFrequency') {
                    value = value * 60000; // Minutes to milliseconds
                }
            } else if (input.type === 'range') {
                value = parseFloat(input.value);
            } else {
                value = input.value;
            }
            
            updates[settingPath] = value;
        });
        
        this.settingsManager.updateMultiple(updates);
        
        this.unsavedChanges = false;
        this.saveButton.disabled = true;
        this.unsavedIndicator.style.display = 'none';
        
        this.showNotification('Settings saved successfully', 'success');
    }

    /**
     * Handle reset button click
     */
    handleReset() {
        if (confirm('Are you sure you want to reset all settings to defaults?')) {
            this.settingsManager.reset();
            this.loadCurrentSettings();
            this.unsavedChanges = false;
            this.saveButton.disabled = true;
            this.unsavedIndicator.style.display = 'none';
            this.showNotification('Settings reset to defaults', 'info');
        }
    }

    /**
     * Handle export button click
     */
    handleExport() {
        const exportData = this.settingsManager.exportSettings();
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `snap-dashboard-settings-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('Settings exported successfully', 'success');
    }

    /**
     * Handle import button click
     */
    handleImport() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (event) => {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const importData = JSON.parse(e.target.result);
                    const success = this.settingsManager.importSettings(importData);
                    
                    if (success) {
                        this.loadCurrentSettings();
                        this.showNotification('Settings imported successfully', 'success');
                    } else {
                        this.showNotification('Failed to import settings', 'error');
                    }
                } catch (error) {
                    this.showNotification('Invalid settings file', 'error');
                }
            };
            reader.readAsText(file);
        };
        
        input.click();
    }

    /**
     * Update diagnostics display
     */
    updateDiagnostics() {
        // This would integrate with actual system monitoring
        // For now, showing placeholder values
        
        const diagnostics = {
            'current-memory': '256 MB',
            'db-size': '1.2 GB',
            'products-count': '2.5M',
            'sales-count': '8.7M',
            'avg-query-time': '150ms',
            'cache-hit-rate': '94%',
            'error-rate': '0.02%',
            'uptime': '2d 14h 32m'
        };
        
        Object.entries(diagnostics).forEach(([id, value]) => {
            const element = this.container.querySelector(`#${id}`);
            if (element) {
                element.textContent = value;
            }
        });
    }

    /**
     * Clear cache
     */
    clearCache() {
        if (confirm('Are you sure you want to clear all cached data?')) {
            // This would integrate with actual cache clearing
            this.showNotification('Cache cleared successfully', 'success');
        }
    }

    /**
     * Run cleanup
     */
    runCleanup() {
        if (confirm('Are you sure you want to run data cleanup?')) {
            // This would integrate with actual cleanup process
            this.showNotification('Cleanup completed successfully', 'success');
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Simple notification implementation
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

export default SettingsComponent;
