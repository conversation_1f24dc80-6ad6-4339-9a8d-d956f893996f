// Halftone Processor - Core functionality for Snap Image Studio Halftone Processing
// Adapted from snap-halftone implementation for batch processing

// Constants for halftone processing
const DPI = 300;
const FINAL_WIDTH = 4500;
const FINAL_HEIGHT = 5400;
const MARGIN = 50; // margin in pixels
const HALFTONE_ANGLE = 20 * (Math.PI / 180); // 20 degrees in radians
const HALFTONE_FREQUENCY = 32; // Lines per inch (LPI)
const CELL_SIZE = Math.round(DPI / HALFTONE_FREQUENCY);
const BLACK_THRESHOLD = 25; // Values <= 25 will be solid black
const WHITE_THRESHOLD = 230; // Values > 230 will be white with no dots
const USE_WHITE_DOTS = true; // White dots on black background

// Create CRC32 table for PNG chunk validation
const crc32Table = (() => {
  let table;
  const crcTable = [];
  for (let i = 0; i < 256; i++) {
    table = i;
    for (let j = 0; j < 8; j++) {
      table = table & 1 ? 3988292384 ^ (table >>> 1) : table >>> 1;
    }
    crcTable[i] = table;
  }
  return crcTable;
})();

// Calculate CRC32 for PNG chunks
function calculateCRC32(data) {
  let crc = -1;
  for (let i = 0; i < data.length; i++) {
    crc = (crc >>> 8) ^ crc32Table[(crc ^ data[i]) & 0xFF];
  }
  return (~crc >>> 0);
}

// Add DPI information to PNG file
async function setDPI(blob, dpi) {
  return new Promise(resolve => {
    const img = new Image();
    const objectUrl = URL.createObjectURL(blob);
    img.crossOrigin = "Anonymous";
    img.src = objectUrl;
    img.onload = async () => {
      URL.revokeObjectURL(objectUrl);
      
      const buffer = await blob.arrayBuffer();
      const data = new Uint8Array(buffer);
      
      // Verify PNG signature
      const pngSignature = [137, 80, 78, 71, 13, 10, 26, 10];
      for (let i = 0; i < pngSignature.length; i++) {
        if (data[i] !== pngSignature[i]) {
          return resolve(blob); // Not a PNG file, return original
        }
      }
      
      // Convert DPI to pixels per meter (which is what PNG uses)
      const dpiValue = Math.round(dpi * 39.3701); // 39.3701 inches per meter
      
      // Create pHYs chunk data (X DPI, Y DPI, unit specifier - 1 means meters)
      const pHYsData = new Uint8Array([
        dpiValue >>> 24 & 255, dpiValue >>> 16 & 255, dpiValue >>> 8 & 255, dpiValue & 255,
        dpiValue >>> 24 & 255, dpiValue >>> 16 & 255, dpiValue >>> 8 & 255, dpiValue & 255,
        1 // 1 means physical unit is meters
      ]);
      
      // Create pHYs chunk type identifier
      const chunkType = new Uint8Array([112, 72, 89, 115]); // "pHYs" in ASCII
      
      // Calculate CRC for the chunk
      const crc = calculateCRC32([...chunkType, ...pHYsData]);
      
      // Create the full chunk (length + type + data + CRC)
      const chunk = new Uint8Array([
        0, 0, 0, 9, // Length of data (9 bytes)
        ...chunkType,
        ...pHYsData,
        crc >>> 24 & 255, crc >>> 16 & 255, crc >>> 8 & 255, crc & 255
      ]);
      
      // Insert the pHYs chunk after IHDR (position 33)
      const newData = new Uint8Array(data.length + chunk.length);
      newData.set(data.slice(0, 33), 0); // Header + IHDR
      newData.set(chunk, 33); // Insert pHYs chunk
      newData.set(data.slice(33), 33 + chunk.length); // Rest of the file
      
      // Create new blob with DPI information
      const newBlob = new Blob([newData], { type: "image/png" });
      resolve(newBlob);
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(objectUrl);
      resolve(blob); // Return original if error
    };
  });
}

// Photoshop-style Levels adjustment
function photoshopLevels(data, inputBlack, inputMid, inputWhite, outputBlack, outputWhite) {
  // Convert input values from 0-255 range to 0-1 for calculations
  inputBlack = inputBlack / 255;
  inputMid = inputMid; // Gamma value is already in 0-1 range
  inputWhite = inputWhite / 255;
  outputBlack = outputBlack / 255;
  outputWhite = outputWhite / 255;
  
  // Calculate gamma from the middle input value (Photoshop formula)
  const gamma = 1 / inputMid;
  
  for (let i = 0; i < data.length; i += 4) {
    // Get normalized grayscale value (0-1)
    let v = data[i] / 255;
    
    // Apply input levels (black and white points)
    // Map to 0-1 range based on input black/white points
    v = (v - inputBlack) / (inputWhite - inputBlack);
    v = Math.max(0, Math.min(1, v)); // Clamp to 0-1 range
    
    // Apply gamma correction
    v = Math.pow(v, gamma);
    
    // Map to output range
    v = outputBlack + v * (outputWhite - outputBlack);
    v = Math.max(0, Math.min(1, v)); // Clamp to 0-1 range
    
    // Convert back to 0-255 range and set all RGB channels (keeping gray)
    const pixelValue = Math.round(v * 255);
    data[i] = data[i+1] = data[i+2] = pixelValue;
    // Alpha channel remains unchanged
  }
}

// Adjust saturation of RGB image data
function adjustSaturation(data, amount) {
  // amount: positive increases saturation, negative decreases
  // normalized amount between -100 and 100
  const saturation = 1 + (amount / 100);
  
  for (let i = 0; i < data.length; i += 4) {
    // Get RGB values
    const r = data[i];
    const g = data[i+1];
    const b = data[i+2];
    
    // Calculate luminance (gray) using the same formula as grayscale conversion
    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
    
    // Adjust saturation by pushing colors away from gray
    data[i] = Math.max(0, Math.min(255, gray + saturation * (r - gray)));
    data[i+1] = Math.max(0, Math.min(255, gray + saturation * (g - gray)));
    data[i+2] = Math.max(0, Math.min(255, gray + saturation * (b - gray)));
    // Alpha channel remains unchanged
  }
}

// Draw a crisp dot (no anti-aliasing, perfectly round)
function drawCrispDot(ctx, centerX, centerY, radius) {
  // Round to integers to ensure crisp edges
  const cx = Math.round(centerX);
  const cy = Math.round(centerY);
  const r = Math.max(1, radius); // Ensure minimum radius of 1

  if (r <= 1) {
    // For very small dots, just draw a single pixel
    ctx.fillRect(cx, cy, 1, 1);
    return;
  }

  // Use path-based drawing for perfectly round dots (anti-aliased)
  ctx.save();
  ctx.beginPath();
  ctx.arc(cx, cy, r, 0, Math.PI * 2, false);
  ctx.fill();
  ctx.restore();
}

// Get coordinates for rotated grid
function getRotatedCellCoordinates(x, y, cellSize, angle) {
  // Transform to rotated coordinate system
  // First, find which cell this pixel belongs to in the rotated grid
  const cosAngle = Math.cos(angle);
  const sinAngle = Math.sin(angle);
  
  // Rotate the point to find its position in the rotated grid
  const rotX = x * cosAngle + y * sinAngle;
  const rotY = -x * sinAngle + y * cosAngle;
  
  // Find the nearest cell center in the rotated grid
  const cellX = Math.floor(rotX / cellSize) * cellSize + cellSize / 2;
  const cellY = Math.floor(rotY / cellSize) * cellSize + cellSize / 2;
  
  // Rotate back to get the cell center in the original coordinate system
  return {
    centerX: cellX * cosAngle - cellY * sinAngle,
    centerY: cellX * sinAngle + cellY * cosAngle
  };
}

// Core halftone processing function
async function processHalftoneImage(imageFile, progressCallback, abortSignal) {
  return new Promise(async (resolve, reject) => {
    let isSettled = false;
    const safeResolve = (value) => {
      if (!isSettled) {
        isSettled = true;
        resolve(value);
      }
    };
    const safeReject = (error) => {
      if (!isSettled) {
        isSettled = true;
        reject(error);
      }
    };
    try {
      // Cooperative scheduling helpers
      let lastYieldTs = (typeof performance !== 'undefined' ? performance.now() : Date.now());
      const maybeYield = async () => {
        const now = (typeof performance !== 'undefined' ? performance.now() : Date.now());
        if (now - lastYieldTs > 8) {
          await new Promise(r => (typeof requestAnimationFrame !== 'undefined' ? requestAnimationFrame(r) : setTimeout(r, 0)));
          lastYieldTs = (typeof performance !== 'undefined' ? performance.now() : Date.now());
        }
      };
      const checkAbort = () => {
        if (abortSignal && abortSignal.aborted) {
          throw new DOMException('Aborted', 'AbortError');
        }
      };

      // Update progress
      if (progressCallback) {
        progressCallback({ stage: 'loading', progress: 0 });
      }
      
      // Convert file to image
      const imageUrl = URL.createObjectURL(imageFile);
      const img = new Image();
      
      if (abortSignal) {
        abortSignal.addEventListener('abort', () => {
          try { URL.revokeObjectURL(imageUrl); } catch (_) {}
          // Attempt to stop further processing quickly
          try { img.src = ''; } catch (_) {}
          safeReject(new DOMException('Aborted', 'AbortError'));
        }, { once: true });
      }

      img.onload = async () => {
        try {
          URL.revokeObjectURL(imageUrl);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'preparing', progress: 10 });
        }
        
        // Create canvas and context
        const canvas = document.createElement('canvas');
        canvas.width = FINAL_WIDTH;
        canvas.height = FINAL_HEIGHT;
        const ctx = canvas.getContext('2d', { willReadFrequently: true });
        
        // Fill with black background
        ctx.fillStyle = '#000000';
        ctx.fillRect(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        
        // Calculate dimensions for centered image with margin
        let aspectRatio = img.width / img.height;
        let renderWidth = FINAL_WIDTH - (MARGIN * 2);
        let renderHeight = renderWidth / aspectRatio;
        
        if (renderHeight > FINAL_HEIGHT - (MARGIN * 2)) {
          renderHeight = FINAL_HEIGHT - (MARGIN * 2);
          renderWidth = renderHeight * aspectRatio;
        }
        
        // Calculate offsets: center horizontally, align to top with margin
        const offsetX = (FINAL_WIDTH - renderWidth) / 2;
        const offsetY = MARGIN; // Just use top margin instead of centering vertically
        
        // Draw image at the top with horizontal centering
        ctx.drawImage(img, offsetX, offsetY, renderWidth, renderHeight);
        
        // Store original color image for final masking
        const originalColorCanvas = document.createElement('canvas');
        originalColorCanvas.width = FINAL_WIDTH;
        originalColorCanvas.height = FINAL_HEIGHT;
        const originalColorCtx = originalColorCanvas.getContext('2d');
        originalColorCtx.drawImage(canvas, 0, 0);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'enhancing', progress: 20 });
        }
        
        // Get image data for processing
        let imageData = ctx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        let data = imageData.data;
        
        // Apply saturation adjustment (+25)
        adjustSaturation(data, 25);
        ctx.putImageData(imageData, 0, 0);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'grayscale', progress: 30 });
        }
        
        // Convert to grayscale
        imageData = ctx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i], g = data[i+1], b = data[i+2];
          // Using standard luminosity formula
          const gray = Math.round(0.299*r + 0.587*g + 0.114*b);
          data[i] = data[i+1] = data[i+2] = gray;
        }
        
        ctx.putImageData(imageData, 0, 0);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'levels', progress: 40 });
        }
        
        // Apply Levels adjustment
        photoshopLevels(data, 0, 0.78, 65, 0, 255);
        ctx.putImageData(imageData, 0, 0);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'halftone', progress: 50 });
        }
        
        // Get processed grayscale data
        const sourceData = ctx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT).data;
        
        // Create a pixel classification map
        const pixelMap = new Uint8Array(FINAL_WIDTH * FINAL_HEIGHT);
        
        // Mark black and white pixels
        for (let y = 0; y < FINAL_HEIGHT; y++) {
          if ((y & 31) === 0) {
            await maybeYield();
            checkAbort();
          }
          for (let x = 0; x < FINAL_WIDTH; x++) {
            const idx = (y * FINAL_WIDTH + x) * 4;
            const brightness = sourceData[idx];
            
            if (USE_WHITE_DOTS) {
              // For white dots on black, invert the logic
              if (brightness >= 255 - BLACK_THRESHOLD) {
                pixelMap[y * FINAL_WIDTH + x] = 1; // Mark as solid white
              } else if (brightness < 255 - WHITE_THRESHOLD) {
                pixelMap[y * FINAL_WIDTH + x] = 3; // Mark as solid black (no white dots)
              }
            } else {
              // Original logic for black dots on white
              if (brightness <= BLACK_THRESHOLD) {
                pixelMap[y * FINAL_WIDTH + x] = 1; // Mark as solid black
              } else if (brightness > WHITE_THRESHOLD) {
                pixelMap[y * FINAL_WIDTH + x] = 3; // Mark as solid white (no dots)
              }
            }
          }
        }
        
        // Create halftone canvas
        const halftoneCanvas = document.createElement('canvas');
        halftoneCanvas.width = FINAL_WIDTH;
        halftoneCanvas.height = FINAL_HEIGHT;
        const halftoneCtx = halftoneCanvas.getContext('2d', { willReadFrequently: true });
        
        // Disable anti-aliasing
        halftoneCtx.imageSmoothingEnabled = false;
        
        // Fill with background color
        if (USE_WHITE_DOTS) {
          halftoneCtx.fillStyle = '#000000';
        } else {
          halftoneCtx.fillStyle = '#ffffff';
        }
        halftoneCtx.fillRect(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        
        // Draw solid areas
        for (let y = 0; y < FINAL_HEIGHT; y++) {
          if ((y & 63) === 0) {
            await maybeYield();
            checkAbort();
          }
          for (let x = 0; x < FINAL_WIDTH; x++) {
            if (pixelMap[y * FINAL_WIDTH + x] === 1) {
              if (USE_WHITE_DOTS) {
                halftoneCtx.fillStyle = '#ffffff';
              } else {
                halftoneCtx.fillStyle = '#000000';
              }
              halftoneCtx.fillRect(x, y, 1, 1);
            }
          }
        }
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'dots', progress: 60 });
        }
        
        // Set dot color
        if (USE_WHITE_DOTS) {
          halftoneCtx.fillStyle = '#ffffff';
        } else {
          halftoneCtx.fillStyle = '#000000';
        }
        
        // Track drawn dots
        const drawnDots = new Set();
        
        // Process each pixel for halftone dots
        for (let y = 0; y < FINAL_HEIGHT; y++) {
          // Update progress periodically
          if (y % 100 === 0 && progressCallback) {
            const dotProgress = 60 + Math.floor((y / FINAL_HEIGHT) * 20);
            progressCallback({ stage: 'dots', progress: dotProgress });
          }
          if ((y & 31) === 0) {
            await maybeYield();
            checkAbort();
          }
          
          for (let x = 0; x < FINAL_WIDTH; x++) {
            const pixelIdx = y * FINAL_WIDTH + x;
            
            // Skip solid areas
            if (pixelMap[pixelIdx] === 1 || pixelMap[pixelIdx] === 3) continue;
            
            // Get rotated cell coordinates
            const cellCoords = getRotatedCellCoordinates(x, y, CELL_SIZE, HALFTONE_ANGLE);
            const cellKey = `${Math.round(cellCoords.centerX)},${Math.round(cellCoords.centerY)}`;
            
            // Skip if already processed
            if (drawnDots.has(cellKey)) continue;
            drawnDots.add(cellKey);
            
            const centerX = cellCoords.centerX;
            const centerY = cellCoords.centerY;
            
            // Skip if outside boundaries
            if (centerX < 0 || centerX >= FINAL_WIDTH || centerY < 0 || centerY >= FINAL_HEIGHT) continue;
            
            // Sample area for brightness
            const sampleRadius = Math.floor(CELL_SIZE / 2);
            let totalBrightness = 0;
            let pixelsProcessed = 0;
            
            // Sample in a circle
            for (let sy = -sampleRadius; sy <= sampleRadius; sy++) {
              for (let sx = -sampleRadius; sx <= sampleRadius; sx++) {
                // Skip outside sampling circle
                if (sx*sx + sy*sy > sampleRadius*sampleRadius) continue;
                
                const sampleX = Math.floor(centerX + sx);
                const sampleY = Math.floor(centerY + sy);
                
                // Skip outside image
                if (sampleX < 0 || sampleX >= FINAL_WIDTH || sampleY < 0 || sampleY >= FINAL_HEIGHT) continue;
                
                const samplePixelIdx = sampleY * FINAL_WIDTH + sampleX;
                const sampleDataIdx = samplePixelIdx * 4;
                
                // Skip solid areas
                if (pixelMap[samplePixelIdx] === 1 || pixelMap[samplePixelIdx] === 3) continue;
                
                totalBrightness += sourceData[sampleDataIdx];
                pixelsProcessed++;
              }
            }
            
            // Skip if no valid samples
            if (pixelsProcessed === 0) continue;
            
            // Calculate average brightness
            const avgBrightness = totalBrightness / pixelsProcessed;
            
            // Skip based on threshold
            if (USE_WHITE_DOTS) {
              if (avgBrightness < 255 - WHITE_THRESHOLD || avgBrightness >= 255 - BLACK_THRESHOLD) continue;
            } else {
              if (avgBrightness > WHITE_THRESHOLD) continue;
            }
            
            // Calculate dot size
            let normalizedSize;
            if (USE_WHITE_DOTS) {
              normalizedSize = avgBrightness / 255;
            } else {
              normalizedSize = 1 - (avgBrightness / 255);
            }
            
            // Enhance contrast
            normalizedSize = Math.pow(normalizedSize, 0.65);
            
            // Calculate radius
            const maxRadius = CELL_SIZE * 0.55;
            let radius = maxRadius * normalizedSize;
            
            // Skip tiny dots
            if (radius < 1.5) continue;
            
            // Draw dot
            drawCrispDot(halftoneCtx, centerX, centerY, radius);
          }
        }
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'masking', progress: 80 });
        }
        
        // Create final output canvas
        const finalCanvas = document.createElement('canvas');
        finalCanvas.width = FINAL_WIDTH;
        finalCanvas.height = FINAL_HEIGHT;
        const finalCtx = finalCanvas.getContext('2d');
        
        // Get halftone pattern data
        const halftoneData = halftoneCtx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        const halftonePixels = halftoneData.data;
        
        // Get original color data
        const colorData = originalColorCtx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        const colorPixels = colorData.data;
        
        // Create final image data
        const finalData = finalCtx.createImageData(FINAL_WIDTH, FINAL_HEIGHT);
        const finalPixels = finalData.data;
        
        // Apply masking
        for (let i = 0; i < halftonePixels.length; i += 4) {
          const maskValue = halftonePixels[i];
          
          const isVisible = USE_WHITE_DOTS ? 
            (maskValue > 127) : 
            (maskValue <= 127);
          
          if (isVisible) {
            // Copy color with full alpha
            finalPixels[i] = colorPixels[i];         // R
            finalPixels[i + 1] = colorPixels[i + 1]; // G
            finalPixels[i + 2] = colorPixels[i + 2]; // B
            finalPixels[i + 3] = 255;                // Full alpha
          } else {
            // Set transparent
            finalPixels[i] = 0;
            finalPixels[i + 1] = 0; 
            finalPixels[i + 2] = 0;
            finalPixels[i + 3] = 0;                  // Transparent
          }
        }
        
        // Put final image data
        finalCtx.putImageData(finalData, 0, 0);
        
        // Find the top edge of the image content
        const finalImageData = finalCtx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        let topEdge = 0;
        let leftEdge = FINAL_WIDTH;
        let rightEdge = 0;
        
        // Scan for content boundaries
        for (let y = 0; y < FINAL_HEIGHT; y++) {
          if ((y & 63) === 0) {
            await maybeYield();
            checkAbort();
          }
          for (let x = 0; x < FINAL_WIDTH; x++) {
            const alpha = finalImageData.data[(y * FINAL_WIDTH + x) * 4 + 3];
            if (alpha > 0) {
              if (y < topEdge || topEdge === 0) topEdge = y;
              if (x < leftEdge) leftEdge = x;
              if (x > rightEdge) rightEdge = x;
            }
          }
        }
        
        // Calculate content width and horizontal centering offset
        const contentWidth = rightEdge - leftEdge;
        const horizontalOffset = Math.max(0, Math.floor((FINAL_WIDTH - contentWidth) / 2));
        
        // Create a temporary canvas for the repositioned image
        const positionedCanvas = document.createElement('canvas');
        positionedCanvas.width = FINAL_WIDTH;
        positionedCanvas.height = FINAL_HEIGHT;
        const positionedCtx = positionedCanvas.getContext('2d');
        
        // Clear the canvas (make it transparent)
        positionedCtx.clearRect(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        
        // Draw the image content starting from the detected edges, with centering and top margin
        positionedCtx.drawImage(
          finalCanvas,
          leftEdge, topEdge,                    // Source x, y
          contentWidth, FINAL_HEIGHT - topEdge,  // Source width, height
          horizontalOffset, MARGIN,              // Destination x, y
          contentWidth, FINAL_HEIGHT - topEdge   // Destination width, height
        );
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'finalizing', progress: 90 });
        }
        
        // Convert to blob using the positioned canvas
        checkAbort();
        await maybeYield();
        const blob = await new Promise(resolve => {
          positionedCanvas.toBlob(blob => resolve(blob), 'image/png', 1.0);
        });
        
        // Add DPI metadata
        checkAbort();
        const finalBlob = await setDPI(blob, DPI);
        
        // Ensure the final blob has PNG MIME type
        checkAbort();
        const pngBlob = new Blob([await finalBlob.arrayBuffer()], {type: 'image/png'});
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'complete', progress: 100 });
        }
        
          // Return processed image
          safeResolve(pngBlob);
        } catch (err) {
          safeReject(err);
        }
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(imageUrl);
        safeReject(new Error('Failed to load image'));
      };
      
      img.src = imageUrl;
    } catch (error) {
      safeReject(error);
    }
  });
}

// Apply adaptive compression to image blob
async function applyAdaptiveCompression(blob, applyCompression, abortSignal) {
  // Skip if compression not requested or blob size <= 10MB
  if (!applyCompression || blob.size <= 10485760) {
    if (window.SnapLogger) {
      window.SnapLogger.debug(`Skipping compression: Apply=${applyCompression}, Size=${blob.size} bytes`);
    }
    return blob;
  }
  
  try {
    if (window.SnapLogger) {
      window.SnapLogger.debug(`Starting compression: Original size=${blob.size} bytes`);
    }
    
    // Start with highest compression level
    let compressionLevel = 1024;
    let compressedBlob = null;
    let attempts = 0;
    const MAX_ATTEMPTS = 5;
    const checkAbort = () => {
      if (abortSignal && abortSignal.aborted) {
        throw new DOMException('Aborted', 'AbortError');
      }
    };
    const maybeYield = async () => {
      await new Promise(r => (typeof requestAnimationFrame !== 'undefined' ? requestAnimationFrame(r) : setTimeout(r, 0)));
    };
    
    while (attempts < MAX_ATTEMPTS) {
      attempts++;
      
      // Convert blob to arrayBuffer for UPNG processing
      checkAbort();
      const arrayBuffer = await blob.arrayBuffer();
      
      // Decode PNG using UPNG
      const pngData = UPNG.decode(arrayBuffer);
      
      // Get RGBA8 data
      const rgba8Data = UPNG.toRGBA8(pngData)[0];
      
      // Re-encode with compression
      checkAbort();
      const compressedData = UPNG.encode([rgba8Data], pngData.width, pngData.height, compressionLevel);
      
      // Create new blob from compressed data
      compressedBlob = new Blob([compressedData], { type: 'image/png' });
      
      console.log(`Compression attempt ${attempts} with level ${compressionLevel}, size: ${compressedBlob.size} bytes`);
      
      // Check if size is acceptable or we've reached minimum compression
      if (compressedBlob.size <= 20971520 || compressionLevel <= 128) {
        break;
      }
      
      // Reduce compression level for next attempt
      compressionLevel = Math.floor(compressionLevel / 2);
      await maybeYield();
    }
    
    // Re-apply DPI information to the compressed blob
    checkAbort();
    const finalBlob = await setDPI(compressedBlob, DPI);
    
    console.log(`Final compressed size: ${finalBlob.size} bytes (${Math.round(finalBlob.size / blob.size * 100)}% of original)`);
    return finalBlob;
  } catch (error) {
    console.error('Compression failed:', error);
    return blob; // Return original if compression fails
  }
}

// Process multiple files in a batch
async function processHalftoneBatch(files, options, progressCallback) {
  const results = [];
  const applyCompression = options?.applyCompression || false;
  const abortSignal = options?.abortSignal;
  
  for (let i = 0; i < files.length; i++) {
    try {
      // Update batch progress
      if (progressCallback) {
        progressCallback({
          file: {
            current: i + 1,
            total: files.length,
            name: files[i].name
          },
          stage: 'starting',
          progress: 0
        });
      }
      
      // Process current file with progress updates
      const processedBlob = await processHalftoneImage(
        files[i],
        (progress) => {
          if (progressCallback) {
            progressCallback({
              file: {
                current: i + 1,
                total: files.length,
                name: files[i].name
              },
              ...progress
            });
          }
        },
        abortSignal
      );
      
      // Apply compression if needed and requested
      if (progressCallback) {
        progressCallback({
          file: {
            current: i + 1,
            total: files.length,
            name: files[i].name
          },
          stage: 'compression',
          progress: 95
        });
      }
      
      const finalBlob = await applyAdaptiveCompression(processedBlob, applyCompression, abortSignal);
      
      // Add to results
      const result = {
        original: files[i],
        processed: finalBlob,
        name: files[i].name,
        success: true
      };
      
      results.push(result);
      
      // Final progress update for this file
      if (progressCallback) {
        progressCallback({
          file: {
            current: i + 1,
            total: files.length,
            name: files[i].name
          },
          stage: 'complete',
          progress: 100
        });
      }
    } catch (error) {
      console.error(`Error processing file ${files[i].name}:`, error);
      
      // Add failed result
      results.push({
        original: files[i],
        processed: null,
        name: files[i].name,
        success: false,
        error: error.message
      });
      
      // Error progress update
      if (progressCallback) {
        progressCallback({
          file: {
            current: i + 1,
            total: files.length,
            name: files[i].name
          },
          stage: 'error',
          progress: 0,
          error: error.message
        });
      }
    }
  }
  
  return results;
}

// Export processor functions
window.halftoneProcessor = {
  processHalftoneImage,
  processHalftoneBatch,
  applyAdaptiveCompression
}; 