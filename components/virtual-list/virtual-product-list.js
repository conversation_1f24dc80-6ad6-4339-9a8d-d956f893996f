/**
 * Virtual Product List Component
 * Efficiently displays millions of products using virtual scrolling
 * Only renders visible items to maintain performance with 5M+ products
 */

class VirtualProductList {
    constructor(container, queryManager, options = {}) {
        this.container = container;
        this.queryManager = queryManager;
        
        // Configuration
        this.itemHeight = options.itemHeight || 80;
        this.bufferSize = options.bufferSize || 10;
        this.pageSize = options.pageSize || 100;
        this.searchDebounceMs = options.searchDebounceMs || 300;
        
        // State
        this.items = [];
        this.filteredItems = [];
        this.visibleItems = [];
        this.scrollTop = 0;
        this.containerHeight = 0;
        this.totalHeight = 0;
        this.startIndex = 0;
        this.endIndex = 0;
        this.isLoading = false;
        this.hasMore = true;
        this.currentFilter = {};
        this.currentSort = { field: 'title', direction: 'asc' };
        this.searchQuery = '';
        
        // DOM elements
        this.viewport = null;
        this.scrollContainer = null;
        this.itemsContainer = null;
        this.loadingIndicator = null;
        this.searchInput = null;
        
        // Event handlers
        this.onScroll = this.onScroll.bind(this);
        this.onResize = this.onResize.bind(this);
        this.onSearch = this.debounce(this.handleSearch.bind(this), this.searchDebounceMs);
        
        // Initialize
        this.init();
    }

    /**
     * Initialize the virtual list component
     */
    init() {
        this.createDOM();
        this.attachEventListeners();
        this.updateDimensions();
        this.loadInitialData();
    }

    /**
     * Create DOM structure for virtual list
     */
    createDOM() {
        this.container.innerHTML = `
            <div class="virtual-list">
                <div class="virtual-list-header">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search products..." />
                        <div class="search-results-count"></div>
                    </div>
                    <div class="sort-controls">
                        <select class="sort-field">
                            <option value="title">Title</option>
                            <option value="asin">ASIN</option>
                            <option value="price">Price</option>
                            <option value="lastUpdated">Last Updated</option>
                        </select>
                        <button class="sort-direction" data-direction="asc">↑</button>
                    </div>
                    <div class="filter-controls">
                        <select class="marketplace-filter">
                            <option value="">All Marketplaces</option>
                            <option value="amazon.com">Amazon.com</option>
                            <option value="amazon.co.uk">Amazon.co.uk</option>
                            <option value="amazon.de">Amazon.de</option>
                        </select>
                    </div>
                </div>
                <div class="virtual-list-viewport">
                    <div class="virtual-list-scroll-container">
                        <div class="virtual-list-items"></div>
                    </div>
                    <div class="loading-indicator">
                        <div class="spinner"></div>
                        <span>Loading products...</span>
                    </div>
                </div>
                <div class="virtual-list-footer">
                    <div class="items-info"></div>
                    <div class="performance-stats"></div>
                </div>
            </div>
        `;

        // Get DOM references
        this.viewport = this.container.querySelector('.virtual-list-viewport');
        this.scrollContainer = this.container.querySelector('.virtual-list-scroll-container');
        this.itemsContainer = this.container.querySelector('.virtual-list-items');
        this.loadingIndicator = this.container.querySelector('.loading-indicator');
        this.searchInput = this.container.querySelector('.search-input');
        this.sortField = this.container.querySelector('.sort-field');
        this.sortDirection = this.container.querySelector('.sort-direction');
        this.marketplaceFilter = this.container.querySelector('.marketplace-filter');
        this.resultsCount = this.container.querySelector('.search-results-count');
        this.itemsInfo = this.container.querySelector('.items-info');
        this.performanceStats = this.container.querySelector('.performance-stats');
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        this.viewport.addEventListener('scroll', this.onScroll);
        window.addEventListener('resize', this.onResize);
        
        this.searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.onSearch();
        });
        
        this.sortField.addEventListener('change', () => {
            this.currentSort.field = this.sortField.value;
            this.applySortAndFilter();
        });
        
        this.sortDirection.addEventListener('click', () => {
            const currentDirection = this.sortDirection.dataset.direction;
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            this.sortDirection.dataset.direction = newDirection;
            this.sortDirection.textContent = newDirection === 'asc' ? '↑' : '↓';
            this.currentSort.direction = newDirection;
            this.applySortAndFilter();
        });
        
        this.marketplaceFilter.addEventListener('change', () => {
            this.currentFilter.marketplace = this.marketplaceFilter.value || undefined;
            this.applySortAndFilter();
        });
    }

    /**
     * Update component dimensions
     */
    updateDimensions() {
        const rect = this.viewport.getBoundingClientRect();
        this.containerHeight = rect.height;
        this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight) + this.bufferSize * 2;
        this.updateScrollContainer();
    }

    /**
     * Load initial data
     */
    async loadInitialData() {
        this.showLoading(true);
        
        try {
            const result = await this.queryManager.getProductsPaginated(
                this.currentFilter,
                this.pageSize
            );
            
            this.items = result.data;
            this.filteredItems = [...this.items];
            this.hasMore = result.hasMore;
            this.nextCursor = result.nextCursor;
            
            this.updateDisplay();
            this.updateInfo();
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('Failed to load products');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Handle scroll events
     */
    onScroll() {
        this.scrollTop = this.viewport.scrollTop;
        this.updateVisibleItems();
        
        // Check if we need to load more data
        const scrollBottom = this.scrollTop + this.containerHeight;
        const loadThreshold = this.totalHeight - (this.itemHeight * 20);
        
        if (scrollBottom > loadThreshold && this.hasMore && !this.isLoading) {
            this.loadMoreData();
        }
    }

    /**
     * Handle resize events
     */
    onResize() {
        this.updateDimensions();
        this.updateVisibleItems();
    }

    /**
     * Handle search input
     */
    async handleSearch() {
        if (this.searchQuery.trim() === '') {
            this.filteredItems = [...this.items];
        } else {
            this.showLoading(true);
            
            try {
                const searchResults = await this.queryManager.searchProducts(
                    this.searchQuery,
                    this.currentFilter
                );
                this.filteredItems = searchResults;
            } catch (error) {
                console.error('Error searching products:', error);
                this.showError('Search failed');
            } finally {
                this.showLoading(false);
            }
        }
        
        this.updateDisplay();
        this.updateInfo();
    }

    /**
     * Apply sorting and filtering
     */
    async applySortAndFilter() {
        this.showLoading(true);
        
        try {
            // Reset data and reload with new filters
            this.items = [];
            this.filteredItems = [];
            this.hasMore = true;
            this.nextCursor = null;
            
            const result = await this.queryManager.getProductsPaginated(
                this.currentFilter,
                this.pageSize
            );
            
            this.items = result.data;
            this.filteredItems = this.sortItems([...this.items]);
            this.hasMore = result.hasMore;
            this.nextCursor = result.nextCursor;
            
            this.updateDisplay();
            this.updateInfo();
        } catch (error) {
            console.error('Error applying sort and filter:', error);
            this.showError('Failed to apply filters');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Load more data for infinite scrolling
     */
    async loadMoreData() {
        if (this.isLoading || !this.hasMore) return;
        
        this.isLoading = true;
        this.showLoading(true);
        
        try {
            const result = await this.queryManager.getProductsPaginated(
                this.currentFilter,
                this.pageSize,
                this.nextCursor
            );
            
            this.items.push(...result.data);
            
            if (this.searchQuery.trim() === '') {
                this.filteredItems.push(...this.sortItems(result.data));
            }
            
            this.hasMore = result.hasMore;
            this.nextCursor = result.nextCursor;
            
            this.updateDisplay();
            this.updateInfo();
        } catch (error) {
            console.error('Error loading more data:', error);
            this.showError('Failed to load more products');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    /**
     * Update visible items based on scroll position
     */
    updateVisibleItems() {
        const startIndex = Math.floor(this.scrollTop / this.itemHeight);
        const endIndex = Math.min(
            startIndex + this.visibleCount,
            this.filteredItems.length
        );
        
        this.startIndex = Math.max(0, startIndex - this.bufferSize);
        this.endIndex = Math.min(endIndex + this.bufferSize, this.filteredItems.length);
        
        this.renderVisibleItems();
        this.updatePerformanceStats();
    }

    /**
     * Render visible items in the viewport
     */
    renderVisibleItems() {
        const fragment = document.createDocumentFragment();
        
        for (let i = this.startIndex; i < this.endIndex; i++) {
            const item = this.filteredItems[i];
            if (!item) continue;
            
            const itemElement = this.createItemElement(item, i);
            fragment.appendChild(itemElement);
        }
        
        this.itemsContainer.innerHTML = '';
        this.itemsContainer.appendChild(fragment);
        
        // Update container positioning
        this.itemsContainer.style.transform = `translateY(${this.startIndex * this.itemHeight}px)`;
    }

    /**
     * Create DOM element for a single item
     */
    createItemElement(item, index) {
        const element = document.createElement('div');
        element.className = 'virtual-list-item';
        element.style.height = `${this.itemHeight}px`;
        element.dataset.index = index;
        
        // Highlight search terms
        const title = this.highlightSearchTerms(item.title || 'Untitled');
        const description = this.highlightSearchTerms(
            (item.description || '').substring(0, 100) + '...'
        );
        
        element.innerHTML = `
            <div class="item-content">
                <div class="item-image">
                    <img src="${item.imageUrl || '/placeholder.jpg'}" alt="${item.title}" loading="lazy" />
                </div>
                <div class="item-details">
                    <div class="item-title">${title}</div>
                    <div class="item-asin">ASIN: ${item.asin}</div>
                    <div class="item-description">${description}</div>
                    <div class="item-meta">
                        <span class="marketplace">${item.marketplace || 'Unknown'}</span>
                        <span class="price">$${(item.price || 0).toFixed(2)}</span>
                        <span class="updated">${this.formatDate(item.lastUpdated)}</span>
                    </div>
                </div>
                <div class="item-actions">
                    <button class="btn-view" data-asin="${item.asin}">View</button>
                    <button class="btn-edit" data-asin="${item.asin}">Edit</button>
                </div>
            </div>
        `;
        
        // Add click handlers
        element.querySelector('.btn-view').addEventListener('click', () => {
            this.onItemView(item);
        });
        
        element.querySelector('.btn-edit').addEventListener('click', () => {
            this.onItemEdit(item);
        });
        
        return element;
    }

    /**
     * Update scroll container height
     */
    updateScrollContainer() {
        this.totalHeight = this.filteredItems.length * this.itemHeight;
        this.scrollContainer.style.height = `${this.totalHeight}px`;
    }

    /**
     * Update display after data changes
     */
    updateDisplay() {
        this.updateScrollContainer();
        this.updateVisibleItems();
        this.viewport.scrollTop = 0;
        this.scrollTop = 0;
    }

    /**
     * Sort items based on current sort settings
     */
    sortItems(items) {
        return items.sort((a, b) => {
            const aVal = a[this.currentSort.field] || '';
            const bVal = b[this.currentSort.field] || '';
            
            let comparison = 0;
            if (typeof aVal === 'string') {
                comparison = aVal.localeCompare(bVal);
            } else {
                comparison = aVal - bVal;
            }
            
            return this.currentSort.direction === 'desc' ? -comparison : comparison;
        });
    }

    /**
     * Highlight search terms in text
     */
    highlightSearchTerms(text) {
        if (!this.searchQuery.trim()) return text;
        
        const terms = this.searchQuery.trim().split(' ');
        let highlightedText = text;
        
        terms.forEach(term => {
            const regex = new RegExp(`(${term})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    /**
     * Format date for display
     */
    formatDate(date) {
        if (!date) return 'Unknown';
        return new Date(date).toLocaleDateString();
    }

    /**
     * Update info display
     */
    updateInfo() {
        const total = this.filteredItems.length;
        const showing = Math.min(this.endIndex - this.startIndex, total);
        
        this.resultsCount.textContent = `${total.toLocaleString()} products found`;
        this.itemsInfo.textContent = `Showing ${showing} of ${total.toLocaleString()} products`;
    }

    /**
     * Update performance statistics
     */
    updatePerformanceStats() {
        const renderedItems = this.endIndex - this.startIndex;
        const memoryUsage = this.estimateMemoryUsage();
        
        this.performanceStats.innerHTML = `
            Rendered: ${renderedItems} | 
            Memory: ~${memoryUsage}MB | 
            FPS: ${this.getFPS()}
        `;
    }

    /**
     * Show/hide loading indicator
     */
    showLoading(show) {
        this.loadingIndicator.style.display = show ? 'flex' : 'none';
    }

    /**
     * Show error message
     */
    showError(message) {
        // Implementation for error display
        console.error(message);
    }

    /**
     * Handle item view action
     */
    onItemView(item) {
        // Emit custom event for item view
        this.container.dispatchEvent(new CustomEvent('itemView', { detail: item }));
    }

    /**
     * Handle item edit action
     */
    onItemEdit(item) {
        // Emit custom event for item edit
        this.container.dispatchEvent(new CustomEvent('itemEdit', { detail: item }));
    }

    /**
     * Estimate memory usage
     */
    estimateMemoryUsage() {
        const itemSize = 2; // Estimated KB per rendered item
        const renderedItems = this.endIndex - this.startIndex;
        return ((renderedItems * itemSize) / 1024).toFixed(1);
    }

    /**
     * Get current FPS (simplified)
     */
    getFPS() {
        return '60'; // Placeholder - would need actual FPS monitoring
    }

    /**
     * Debounce utility function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Destroy the component and clean up
     */
    destroy() {
        this.viewport.removeEventListener('scroll', this.onScroll);
        window.removeEventListener('resize', this.onResize);
        this.container.innerHTML = '';
    }
}

export default VirtualProductList;
