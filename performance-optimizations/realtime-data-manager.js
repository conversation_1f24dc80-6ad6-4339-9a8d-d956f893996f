/**
 * Real-Time Data Manager for Amazon Integration
 * Handles data fetching, caching, and memory management for Chrome extension
 */

class RealTimeDataManager {
  constructor() {
    this.isActive = false;
    this.fetchInterval = null;
    this.fetchFrequency = 30000; // 30 seconds default
    this.maxRetries = 3;
    this.retryDelay = 5000; // 5 seconds
    this.dataBuffer = new Map(); // Temporary buffer for batching
    this.maxBufferSize = 100;
    this.lastFetchTime = 0;
    this.errorCount = 0;
    this.maxErrors = 10;
    
    // Data type configurations
    this.dataTypes = {
      salesData: { priority: 1, batchSize: 50 },
      listingsData: { priority: 2, batchSize: 100 },
      adSpendData: { priority: 3, batchSize: 20 },
      analyticsData: { priority: 4, batchSize: 30 },
      accountStatus: { priority: 5, batchSize: 10 },
      chartData: { priority: 6, batchSize: 20 }
    };
  }

  /**
   * Start real-time data fetching
   */
  async startRealTimeUpdates() {
    if (this.isActive) {
      if (window.SnapLogger) {
        window.SnapLogger.warn('⚠️ Real-time updates already active');
      }
      return;
    }

    // Skip real-time updates entirely in zero-data mock mode
    if (window.USE_MOCK_ZERO_DATA || (window.MockZeroData && window.MockZeroData.isEnabled())) {
      if (window.SnapLogger) {
        window.SnapLogger.info('🧪 Mock Zero Data enabled: skipping real-time updates');
      }
      return;
    }

    if (window.SnapLogger) {
      window.SnapLogger.info('🚀 Starting real-time Amazon data updates...');
    }
    this.isActive = true;
    this.errorCount = 0;

    // Initial fetch
    await this.fetchAllData();

    // Setup interval using tracked interval
    this.fetchInterval = window.EventCleanupManager.setInterval(
      () => this.fetchAllData(),
      this.fetchFrequency
    );

    if (window.SnapLogger) {
      window.SnapLogger.info(`✅ Real-time updates started (${this.fetchFrequency}ms interval)`);
    }
  }

  /**
   * Stop real-time data fetching
   */
  stopRealTimeUpdates() {
    if (!this.isActive) return;

    if (window.SnapLogger) {
      window.SnapLogger.info('🛑 Stopping real-time updates...');
    }
    this.isActive = false;

    if (this.fetchInterval) {
      window.EventCleanupManager.clearInterval(this.fetchInterval);
      this.fetchInterval = null;
    }

    // Flush any remaining buffered data
    this.flushDataBuffer();

    if (window.SnapLogger) {
      window.SnapLogger.info('✅ Real-time updates stopped');
    }
  }

  /**
   * Fetch all data types with error handling
   */
  async fetchAllData() {
    if (!this.isActive) return;
    
    const startTime = Date.now();
    console.log('🔄 Fetching real-time Amazon data...');
    
    try {
      // Fetch data in priority order
      const sortedTypes = Object.entries(this.dataTypes)
        .sort(([,a], [,b]) => a.priority - b.priority);
      
      for (const [dataType, config] of sortedTypes) {
        if (!this.isActive) break; // Stop if deactivated during fetch
        
        try {
          await this.fetchDataType(dataType, config);
        } catch (error) {
          console.error(`❌ Failed to fetch ${dataType}:`, error);
          this.handleFetchError(dataType, error);
        }
      }
      
      // Flush buffered data to IndexedDB
      await this.flushDataBuffer();
      
      this.lastFetchTime = Date.now();
      const duration = this.lastFetchTime - startTime;
      console.log(`✅ Data fetch completed in ${duration}ms`);
      
      // Reset error count on successful fetch
      this.errorCount = 0;
      
    } catch (error) {
      console.error('❌ Critical error in fetchAllData:', error);
      this.handleCriticalError(error);
    }
  }

  /**
   * Fetch specific data type from Amazon
   */
  async fetchDataType(dataType, config) {
    // This would be replaced with actual Amazon API calls
    // For now, simulate the fetch
    
    console.log(`📡 Fetching ${dataType}...`);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
    
    // Generate mock data (replace with real Amazon API calls)
    const mockData = this.generateMockData(dataType, config.batchSize);
    
    // Add to buffer for batching
    this.addToBuffer(dataType, mockData);
    
    return mockData;
  }

  /**
   * Add data to buffer for efficient batching
   */
  addToBuffer(dataType, data) {
    if (!this.dataBuffer.has(dataType)) {
      this.dataBuffer.set(dataType, []);
    }
    
    const buffer = this.dataBuffer.get(dataType);
    buffer.push(...(Array.isArray(data) ? data : [data]));
    
    // Auto-flush if buffer gets too large
    if (buffer.length > this.maxBufferSize) {
      this.flushDataType(dataType);
    }
  }

  /**
   * Flush all buffered data to IndexedDB
   */
  async flushDataBuffer() {
    for (const [dataType, buffer] of this.dataBuffer.entries()) {
      if (buffer.length > 0) {
        await this.flushDataType(dataType);
      }
    }
  }

  /**
   * Flush specific data type to IndexedDB
   */
  async flushDataType(dataType) {
    const buffer = this.dataBuffer.get(dataType);
    if (!buffer || buffer.length === 0) return;
    
    try {
      console.log(`💾 Flushing ${buffer.length} ${dataType} records to IndexedDB...`);
      
      // Store each record in IndexedDB
      for (const record of buffer) {
        await window.IndexedDBManager.storeAmazonData(dataType, record);
      }
      
      // Clear buffer
      this.dataBuffer.set(dataType, []);
      
      // Update UI with new data
      this.updateUI(dataType, buffer);
      
    } catch (error) {
      console.error(`❌ Failed to flush ${dataType} to IndexedDB:`, error);
      throw error;
    }
  }

  /**
   * Update UI with new data (memory-efficient)
   */
  updateUI(dataType, newData) {
    try {
      // Use the existing real-time update system but with memory management
      if (window.handleRealTimeDataUpdate) {
        // Map data type keys to what the UI handler expects
        const keyMapping = {
          'salesData': 'analytics',      // Sales data goes to analytics
          'listingsData': 'listings',    // Listings data maps directly
          'adSpendData': 'adSpend',       // Ad spend data maps directly
          'analyticsData': 'analytics',   // Analytics data maps directly
          'accountStatus': 'accountStatus', // Account status maps directly
          'chartData': 'chartData'       // Chart data maps directly
        };

        const mappedKey = keyMapping[dataType] || dataType;

        // Create update object without deep copying
        const updateData = {};
        updateData[mappedKey] = newData;

        console.log(`🔄 Mapping ${dataType} → ${mappedKey} for UI update`);

        // Call update function
        window.handleRealTimeDataUpdate(updateData);
      }

    } catch (error) {
      console.error(`❌ Failed to update UI for ${dataType}:`, error);
    }
  }

  /**
   * Handle fetch errors with exponential backoff
   */
  handleFetchError(dataType, error) {
    this.errorCount++;
    
    console.error(`❌ Fetch error for ${dataType} (${this.errorCount}/${this.maxErrors}):`, error);
    
    if (this.errorCount >= this.maxErrors) {
      console.error('❌ Too many errors, stopping real-time updates');
      this.stopRealTimeUpdates();
      return;
    }
    
    // Implement exponential backoff
    const backoffDelay = this.retryDelay * Math.pow(2, this.errorCount - 1);
    console.log(`⏳ Retrying in ${backoffDelay}ms...`);
    
    setTimeout(() => {
      if (this.isActive) {
        this.fetchDataType(dataType, this.dataTypes[dataType]);
      }
    }, backoffDelay);
  }

  /**
   * Handle critical errors
   */
  handleCriticalError(error) {
    console.error('💥 Critical error in real-time data manager:', error);
    
    // Stop updates and notify user
    this.stopRealTimeUpdates();
    
    // Could dispatch event for UI notification
    window.dispatchEvent(new CustomEvent('realTimeDataError', {
      detail: { error: error.message }
    }));
  }

  /**
   * Generate mock data (replace with real Amazon API integration)
   */
  generateMockData(dataType, count) {
    const data = [];
    
    for (let i = 0; i < count; i++) {
      switch (dataType) {
        case 'salesData':
          data.push({
            id: `sale_${Date.now()}_${i}`,
            asin: `B0${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
            marketplace: ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'][Math.floor(Math.random() * 7)],
            units: Math.floor(Math.random() * 10) + 1,
            royalties: (Math.random() * 50).toFixed(2),
            timestamp: Date.now() - Math.random() * 3600000 // Last hour
          });
          break;
          
        case 'listingsData':
          data.push({
            asin: `B0${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
            title: `Product ${i} - ${Math.random().toString(36).substr(2, 5)}`,
            status: ['live', 'draft', 'review'][Math.floor(Math.random() * 3)],
            lastUpdated: Date.now()
          });
          break;

        case 'adSpendData':
          data.push({
            marketplace: ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'][Math.floor(Math.random() * 7)],
            spend: (Math.random() * 500).toFixed(2),
            orders: Math.floor(Math.random() * 50),
            acos: (Math.random() * 30).toFixed(1) + '%',
            timestamp: Date.now()
          });
          break;

        case 'analyticsData':
          data.push({
            totalSales: Math.floor(Math.random() * 1000),
            totalRoyalties: (Math.random() * 5000).toFixed(2),
            totalUnits: Math.floor(Math.random() * 500),
            timestamp: Date.now()
          });
          break;

        case 'accountStatus':
          data.push({
            status: ['active', 'warning', 'suspended'][Math.floor(Math.random() * 3)],
            balance: (Math.random() * 10000).toFixed(2),
            pendingPayments: (Math.random() * 1000).toFixed(2),
            lastSync: Date.now()
          });
          break;

        case 'chartData':
          data.push({
            date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            sales: Math.floor(Math.random() * 100),
            royalties: (Math.random() * 500).toFixed(2),
            units: Math.floor(Math.random() * 50)
          });
          break;

        default:
          data.push({
            id: `${dataType}_${Date.now()}_${i}`,
            value: Math.random() * 100,
            timestamp: Date.now()
          });
      }
    }
    
    return data;
  }

  /**
   * Get manager statistics
   */
  getStats() {
    return {
      isActive: this.isActive,
      lastFetchTime: this.lastFetchTime,
      errorCount: this.errorCount,
      bufferSizes: Object.fromEntries(
        Array.from(this.dataBuffer.entries()).map(([key, value]) => [key, value.length])
      ),
      fetchFrequency: this.fetchFrequency
    };
  }

  /**
   * Update fetch frequency
   */
  setFetchFrequency(frequency) {
    this.fetchFrequency = Math.max(5000, frequency); // Minimum 5 seconds

    if (this.isActive) {
      this.stopRealTimeUpdates();
      this.startRealTimeUpdates();
    }
  }

  /**
   * Enhanced batching system for massive scale data processing
   * @param {Array} dataArray - Array of data to process
   * @param {number} batchSize - Size of each batch
   * @returns {Promise<Object>} Processing result
   */
  async processMassiveDataBatch(dataArray, batchSize = 5000) {
    if (!dataArray || dataArray.length === 0) return { processed: 0, errors: 0 };

    let processedCount = 0;
    let errorCount = 0;
    const startTime = Date.now();

    try {
      for (let i = 0; i < dataArray.length; i += batchSize) {
        const batch = dataArray.slice(i, i + batchSize);

        try {
          // Process batch with deduplication
          const deduplicatedBatch = this.deduplicateData(batch);

          // Store batch using bulk operations
          await window.IndexedDBManager.bulkPut('products', deduplicatedBatch);

          processedCount += deduplicatedBatch.length;

          // Update progress
          this.updateProcessingProgress(i + batch.length, dataArray.length);

          // Allow UI to remain responsive
          if (i % (batchSize * 4) === 0) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }

        } catch (batchError) {
          console.error('❌ Batch processing error:', batchError);
          errorCount += batch.length;
        }
      }

      const duration = Date.now() - startTime;
      console.log(`📦 Processed ${processedCount} records in ${duration}ms (${Math.round(processedCount / (duration / 1000))} records/sec)`);

      return {
        processed: processedCount,
        errors: errorCount,
        duration,
        recordsPerSecond: Math.round(processedCount / (duration / 1000))
      };

    } catch (error) {
      console.error('❌ Massive data processing failed:', error);
      throw error;
    }
  }

  /**
   * Intelligent data deduplication for incoming data streams
   * @param {Array} dataArray - Array of data to deduplicate
   * @returns {Array} Deduplicated data
   */
  deduplicateData(dataArray) {
    const seen = new Set();
    const deduplicated = [];

    for (const item of dataArray) {
      const key = item.asin || item.id || JSON.stringify(item);

      if (!seen.has(key)) {
        seen.add(key);
        deduplicated.push(item);
      }
    }

    const duplicateCount = dataArray.length - deduplicated.length;
    if (duplicateCount > 0) {
      console.log(`🔄 Removed ${duplicateCount} duplicate records`);
    }

    return deduplicated;
  }

  /**
   * Incremental updates that only process changed data
   * @param {Array} newData - New data to compare
   * @param {Array} existingData - Existing data for comparison
   * @returns {Object} Incremental update result
   */
  async processIncrementalUpdates(newData, existingData) {
    const changes = {
      added: [],
      updated: [],
      removed: []
    };

    // Create lookup maps for efficient comparison
    const existingMap = new Map();
    existingData.forEach(item => {
      const key = item.asin || item.id;
      existingMap.set(key, item);
    });

    const newMap = new Map();
    newData.forEach(item => {
      const key = item.asin || item.id;
      newMap.set(key, item);
    });

    // Find added and updated items
    for (const [key, newItem] of newMap) {
      const existingItem = existingMap.get(key);

      if (!existingItem) {
        changes.added.push(newItem);
      } else if (this.hasItemChanged(existingItem, newItem)) {
        changes.updated.push(newItem);
      }
    }

    // Find removed items
    for (const [key, existingItem] of existingMap) {
      if (!newMap.has(key)) {
        changes.removed.push(existingItem);
      }
    }

    // Apply changes
    if (changes.added.length > 0) {
      await window.IndexedDBManager.bulkPut('products', changes.added);
    }

    if (changes.updated.length > 0) {
      await window.IndexedDBManager.bulkPut('products', changes.updated);
    }

    if (changes.removed.length > 0) {
      const keysToRemove = changes.removed.map(item => item.asin || item.id);
      await window.IndexedDBManager.bulkDelete('products', keysToRemove);
    }

    console.log(`🔄 Incremental update: +${changes.added.length} ~${changes.updated.length} -${changes.removed.length}`);

    return changes;
  }

  /**
   * Priority-based data processing for critical updates
   * @param {Array} dataArray - Data to process
   * @param {Function} priorityFunction - Function to determine priority
   * @returns {Promise<Object>} Processing result
   */
  async processPriorityData(dataArray, priorityFunction) {
    // Sort data by priority (high to low)
    const prioritizedData = dataArray.sort((a, b) => {
      return priorityFunction(b) - priorityFunction(a);
    });

    // Process high-priority items first
    const highPriority = prioritizedData.filter(item => priorityFunction(item) > 0.7);
    const normalPriority = prioritizedData.filter(item => priorityFunction(item) <= 0.7);

    let results = { processed: 0, errors: 0 };

    // Process high-priority items immediately
    if (highPriority.length > 0) {
      const highPriorityResult = await this.processMassiveDataBatch(highPriority, 1000);
      results.processed += highPriorityResult.processed;
      results.errors += highPriorityResult.errors;
    }

    // Process normal priority items in background
    if (normalPriority.length > 0) {
      setTimeout(async () => {
        const normalPriorityResult = await this.processMassiveDataBatch(normalPriority, 5000);
        console.log(`📦 Background processing completed: ${normalPriorityResult.processed} records`);
      }, 100);
    }

    return results;
  }

  /**
   * Enhanced error handling for massive dataset scenarios
   * @param {Error} error - Error to handle
   * @param {Object} context - Error context
   */
  handleMassiveScaleError(error, context = {}) {
    const errorInfo = {
      message: error.message,
      timestamp: new Date().toISOString(),
      context,
      memoryUsage: window.MemoryMonitor?.getCurrentUsage() || 'unknown'
    };

    // Log error with context
    console.error('❌ Massive scale error:', errorInfo);

    // Check if error is due to memory pressure
    if (error.message.includes('memory') || error.message.includes('quota')) {
      console.warn('⚠️ Memory pressure detected, triggering cleanup');
      window.MemoryMonitor?.forceCleanup();
    }

    // Check if error is due to transaction timeout
    if (error.message.includes('timeout') || error.message.includes('transaction')) {
      console.warn('⚠️ Transaction timeout detected, reducing batch size');
      this.adaptiveBatchSize = Math.max(100, this.adaptiveBatchSize * 0.5);
    }

    // Emit error event for monitoring
    document.dispatchEvent(new CustomEvent('massiveScaleError', {
      detail: errorInfo
    }));
  }

  /**
   * Data validation and sanitization for incoming streams
   * @param {Array} dataArray - Data to validate
   * @returns {Object} Validation result
   */
  validateAndSanitizeData(dataArray) {
    const valid = [];
    const invalid = [];
    const sanitized = [];

    for (const item of dataArray) {
      try {
        // Basic validation
        if (!item || typeof item !== 'object') {
          invalid.push({ item, reason: 'Invalid object' });
          continue;
        }

        // Sanitize and validate required fields
        const sanitizedItem = this.sanitizeDataItem(item);

        if (this.isValidDataItem(sanitizedItem)) {
          valid.push(sanitizedItem);
        } else {
          invalid.push({ item: sanitizedItem, reason: 'Failed validation' });
        }

      } catch (error) {
        invalid.push({ item, reason: error.message });
      }
    }

    if (invalid.length > 0) {
      console.warn(`⚠️ Data validation: ${invalid.length} invalid items out of ${dataArray.length}`);
    }

    return {
      valid,
      invalid,
      validCount: valid.length,
      invalidCount: invalid.length,
      validationRate: (valid.length / dataArray.length) * 100
    };
  }

  // Private helper methods
  updateProcessingProgress(current, total) {
    const progress = Math.round((current / total) * 100);
    if (progress % 10 === 0) { // Log every 10%
      console.log(`📊 Processing progress: ${progress}% (${current.toLocaleString()}/${total.toLocaleString()})`);
    }
  }

  hasItemChanged(existingItem, newItem) {
    // Compare key fields to detect changes
    const keyFields = ['title', 'price', 'status', 'lastUpdated'];

    for (const field of keyFields) {
      if (existingItem[field] !== newItem[field]) {
        return true;
      }
    }

    return false;
  }

  sanitizeDataItem(item) {
    const sanitized = { ...item };

    // Sanitize strings
    if (sanitized.title) {
      sanitized.title = sanitized.title.toString().trim().substring(0, 500);
    }

    // Sanitize numbers
    if (sanitized.price) {
      sanitized.price = parseFloat(sanitized.price) || 0;
    }

    // Sanitize dates
    if (sanitized.lastUpdated) {
      sanitized.lastUpdated = new Date(sanitized.lastUpdated).toISOString();
    }

    return sanitized;
  }

  isValidDataItem(item) {
    // Basic validation rules
    return item.asin &&
           typeof item.asin === 'string' &&
           item.asin.length > 0 &&
           item.title &&
           typeof item.title === 'string';
  }
}

// Global instance
window.RealTimeDataManager = new RealTimeDataManager();

// Auto-cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.RealTimeDataManager.stopRealTimeUpdates();
});

// Export for global use
window.startRealTimeUpdates = () => window.RealTimeDataManager.startRealTimeUpdates();
window.stopRealTimeUpdates = () => window.RealTimeDataManager.stopRealTimeUpdates();
window.getRealTimeStats = () => window.RealTimeDataManager.getStats();
