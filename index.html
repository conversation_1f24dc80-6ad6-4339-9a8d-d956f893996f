<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Snap App</title>
  
  <!-- Amazon Ember Font Family - Load before other styles for proper font availability -->
  <style>
    @font-face {
      font-family: 'Amazon Ember';
      src: url('fonts/AmazonEmber_Regular.woff2') format('woff2'),
           url('fonts/AmazonEmber_Regular.woff') format('woff'),
           url('fonts/AmazonEmber_Regular.ttf') format('truetype');
      font-weight: 400;
      font-style: normal;
      font-display: swap;
    }
    
    @font-face {
      font-family: 'Amazon Ember';
      src: url('fonts/AmazonEmber_Bold.woff2') format('woff2'),
           url('fonts/AmazonEmber_Bold.woff') format('woff'),
           url('fonts/AmazonEmber_Bold.ttf') format('truetype');
      font-weight: 700;
      font-style: normal;
      font-display: swap;
    }
    
    @font-face {
      font-family: 'Amazon Ember';
      src: url('fonts/Amazon-Ember-Medium.woff2') format('woff2'),
           url('fonts/Amazon-Ember-Medium.woff') format('woff'),
           url('fonts/Amazon-Ember-Medium.ttf') format('truetype');
      font-weight: 500;
      font-style: normal;
      font-display: swap;
    }
  </style>
  
  <!-- Global styles remain as external CSS -->
  <link rel="stylesheet" href="snapapp.css">
  
  <!-- Chart System Styles -->
  <link rel="stylesheet" href="components/charts/snap-charts.css">
</head>
<body>
  <!-- The entire app structure will be created by JavaScript -->
  
  <!-- NoScript Fallback: Display user-friendly message when JavaScript is disabled -->
  <noscript>
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-family: 'Amazon Ember', 'AmazonEmber', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      z-index: var(--z-modal);
    ">
      <div style="max-width: 500px; padding: 2rem;">
        <h1 style="color: #dc3545; margin-bottom: 1rem;">JavaScript Required</h1>
        <p style="color: #6c757d; line-height: 1.6; margin-bottom: 1.5rem;">
          Snap App requires JavaScript to function properly. Please enable JavaScript in your browser settings and refresh the page.
        </p>
        <p style="color: #6c757d; font-size: 0.9rem;">
          If you continue to experience issues, please contact support or try using a different browser.
        </p>
      </div>
    </div>
  </noscript>
  
  <!-- ========================================
       SCRIPT LOADING STRATEGY & DEPENDENCY ORDER
       ========================================
       
       Loading Order Strategy:
       1. Core Utilities (logger, mock data) - Load first for debugging and configuration
       2. Performance Optimization Scripts - Load early to optimize subsequent operations
       3. Utility Scripts - Timezone, date management, content management
       4. Chart System - Data visualization components
       5. Mock Data Scripts - External data sources for development/testing
       6. Main Application - Final script that initializes the entire application
       
       Error Handling: Each script includes onerror attributes to log failures and provide
       fallback behavior. Critical scripts will trigger user notifications on load failure.
       
       Lazy Loading: Performance optimization scripts implement lazy loading strategies
       for non-critical resources to improve initial page load performance.
       ======================================== -->
  
  <!-- Logger (load first to control console output) -->
  <script src="utils/logger.js" onerror="console.error('Failed to load logger.js - logging functionality unavailable')"></script>

  <!-- Security utilities (load early for sanitization functions) -->
  <script src="utils/sanitizeHTML.js" onerror="console.error('Failed to load sanitizeHTML.js - HTML sanitization unavailable')"></script>
  <script src="utils/domHelpers.js" onerror="console.error('Failed to load domHelpers.js - DOM helper functions unavailable')"></script>

  <!-- Mock Zero Data toggle and helpers (set USE_MOCK_ZERO_DATA here) -->
  <script src="utils/mock-zero-data.js" onerror="console.error('Failed to load mock-zero-data.js - mock data functionality unavailable')"></script>

  <!-- Performance Optimization Scripts -->
  <link rel="stylesheet" href="performance-optimizations/lazy-loading-styles.css">
  <script src="performance-optimizations/event-cleanup-manager.js" onerror="console.error('Failed to load event-cleanup-manager.js - event cleanup unavailable')"></script>
  <script src="performance-optimizations/data-cache-manager.js" onerror="console.error('Failed to load data-cache-manager.js - caching functionality unavailable')"></script>
  <script src="performance-optimizations/dom-optimizer.js" onerror="console.error('Failed to load dom-optimizer.js - DOM optimization unavailable')"></script>
  <script src="performance-optimizations/viewport-lazy-loader.js" onerror="console.error('Failed to load viewport-lazy-loader.js - lazy loading unavailable')"></script>
  <script src="performance-optimizations/indexeddb-manager.js" onerror="console.error('Failed to load indexeddb-manager.js - local storage unavailable')"></script>
  <script src="performance-optimizations/realtime-data-manager.js" onerror="console.error('Failed to load realtime-data-manager.js - real-time updates unavailable')"></script>
  <script src="performance-optimizations/memory-monitor.js" onerror="console.error('Failed to load memory-monitor.js - memory monitoring unavailable')"></script>
  <script src="performance-optimizations/daily-sales-manager.js" onerror="console.error('Failed to load daily-sales-manager.js - daily sales tracking unavailable')"></script>
  <script src="performance-optimizations/products-page-manager.js" onerror="console.error('Failed to load products-page-manager.js - products management unavailable')"></script>

  <!-- Timezone Utility Script -->
  <script src="utils/timezone.js" onerror="console.error('Failed to load timezone.js - timezone functionality unavailable')"></script>

  <!-- Date Change Manager Script -->
  <script src="utils/date-change-manager.js" onerror="console.error('Failed to load date-change-manager.js - date management unavailable')"></script>

  <!-- Content Class Manager Script -->
  <script src="utils/content-class-manager.js" onerror="console.error('Failed to load content-class-manager.js - content management unavailable')"></script>

  <!-- Chart System Script -->
  <script src="components/charts/snap-charts.js" onerror="console.error('Failed to load snap-charts.js - chart functionality unavailable')"></script>
  
  <!-- External mock listings for dashboard (today/yesterday) -->
  <script src="components/dashboard/mock-listings.js" onerror="console.error('Failed to load mock-listings.js - mock listings data unavailable')"></script>
  
  <!-- External mock cards (monthly, top four, lifetime) -->
  <script src="components/dashboard/mock-cards.js" onerror="console.error('Failed to load mock-cards.js - mock cards data unavailable')"></script>
  
  <!-- Central dashboard static/mock data overrides -->
  <script src="components/dashboard/mock-dashboard-data.js" onerror="console.error('Failed to load mock-dashboard-data.js - dashboard data unavailable')"></script>

  <!-- Main application script that contains embedded HTML -->
  <script src="snapapp.js" onerror="
    console.error('CRITICAL: Failed to load snapapp.js - application cannot function');
    document.body.innerHTML = '<div style=&quot;text-align: center; padding: 2rem; color: #dc3545;&quot;><h1>Application Error</h1><p>The main application failed to load. Please refresh the page or contact support.</p></div>';
  "></script>
</body>
</html> 