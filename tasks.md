# CSS Optimization and Maintainability Improvements

## Task Overview
Implement CSS improvements based on thorough review comments to enhance maintainability, remove deprecated selectors, and centralize design tokens.

## Implementation Tasks
- [x] Task 1: Remove 'composes: flex-row' usage and apply flex-row styles directly via class names or explicit CSS selectors
- [x] Task 2: Replace all ':contains(...)' pseudo-classes with JavaScript logic that adds/removes specific classes based on element content
- [x] Task 3: Retain ':has()' selectors as optional enhancements but implement JavaScript or class-based fallbacks for browser compatibility
- [x] Task 4: Define CSS variable for success color (#04AE2C) as --color-success in :root selector
- [x] Task 5: Replace hard-coded hex value with CSS variable in .payout-summary-count.positive .payout-summary-currency rule
- [x] Task 6: Review entire stylesheet to centralize all repeated colors and sizes as CSS variables in :root
- [x] Task 7: Move dark theme overrides to [data-theme="dark"] block using variables
- [x] Task 8: Split stylesheet into logical partial files for tokens, base, layout, components, utilities, and theme
- [x] Task 9: Remove any duplicated CSS rules to improve clarity and maintainability

## Status: ✅ ALL TASKS COMPLETED

## Files to Modify
- `snapapp.css` - Main stylesheet for all improvements
- JavaScript files - For implementing :contains() replacement logic

## Current Issues Identified
- Line 2487: `composes: flex-row;` usage
- Line 45: Hard-coded color #04AE2C in .payout-summary-count.positive .payout-summary-currency
- Multiple :contains() pseudo-classes that need JavaScript replacement
- :has() selectors that need fallback implementations
- Duplicated CSS rules throughout the stylesheet
- Scattered color and size values that should be centralized
