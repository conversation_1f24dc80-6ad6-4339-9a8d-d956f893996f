/**
 * Query Manager for Massive Dataset Operations
 * Efficient data access layer for querying 5M+ products and 20M+ sales records
 * Uses cursor-based pagination and indexed queries for optimal performance
 */

class QueryManager {
    constructor(indexedDBManager) {
        this.dbManager = indexedDBManager;
        this.queryCache = new Map();
        this.activeQueries = new Set();
        this.defaultPageSize = 1000;
        this.maxCacheSize = 100;
    }

    /**
     * Get products with pagination using cursor-based approach
     * @param {Object} filter - Filter criteria
     * @param {number} pageSize - Number of records per page
     * @param {string} cursor - Cursor for pagination
     * @returns {Promise<Object>} Paginated results with next cursor
     */
    async getProductsPaginated(filter = {}, pageSize = this.defaultPageSize, cursor = null) {
        const cacheKey = this._generateCacheKey('products', filter, pageSize, cursor);
        
        if (this.queryCache.has(cacheKey)) {
            return this.queryCache.get(cacheKey);
        }

        try {
            const db = await this.dbManager.getDatabase();
            const transaction = db.transaction(['products'], 'readonly');
            const store = transaction.objectStore('products');
            
            let index = store;
            let keyRange = null;

            // Apply filters using appropriate indexes
            if (filter.marketplace) {
                index = store.index('marketplace');
                keyRange = IDBKeyRange.only(filter.marketplace);
            } else if (filter.category) {
                index = store.index('category');
                keyRange = IDBKeyRange.only(filter.category);
            } else if (filter.dateRange) {
                index = store.index('lastUpdated');
                keyRange = IDBKeyRange.bound(filter.dateRange.start, filter.dateRange.end);
            }

            const results = [];
            let count = 0;
            let nextCursor = null;

            return new Promise((resolve, reject) => {
                const request = cursor ? 
                    index.openCursor(keyRange, 'next') : 
                    index.openCursor(keyRange);

                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    
                    if (cursor && count < pageSize) {
                        const product = cursor.value;
                        
                        // Apply additional filters
                        if (this._matchesFilter(product, filter)) {
                            results.push(product);
                            count++;
                        }
                        
                        cursor.continue();
                    } else {
                        nextCursor = cursor ? cursor.key : null;
                        
                        const result = {
                            data: results,
                            nextCursor,
                            hasMore: cursor !== null,
                            totalCount: count
                        };
                        
                        this._cacheResult(cacheKey, result);
                        resolve(result);
                    }
                };

                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error in getProductsPaginated:', error);
            throw error;
        }
    }

    /**
     * Get sales data by date range for specific ASIN
     * @param {Date} startDate - Start date
     * @param {Date} endDate - End date
     * @param {string} asin - Product ASIN
     * @returns {Promise<Array>} Sales data
     */
    async getSalesDataByDateRange(startDate, endDate, asin = null) {
        const cacheKey = this._generateCacheKey('sales', { startDate, endDate, asin });
        
        if (this.queryCache.has(cacheKey)) {
            return this.queryCache.get(cacheKey);
        }

        try {
            const db = await this.dbManager.getDatabase();
            const transaction = db.transaction(['sales'], 'readonly');
            const store = transaction.objectStore('sales');
            const index = asin ? store.index('asin-date') : store.index('date');
            
            let keyRange;
            if (asin) {
                keyRange = IDBKeyRange.bound([asin, startDate], [asin, endDate]);
            } else {
                keyRange = IDBKeyRange.bound(startDate, endDate);
            }

            const results = [];

            return new Promise((resolve, reject) => {
                const request = index.openCursor(keyRange);

                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    
                    if (cursor) {
                        results.push(cursor.value);
                        cursor.continue();
                    } else {
                        this._cacheResult(cacheKey, results);
                        resolve(results);
                    }
                };

                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error in getSalesDataByDateRange:', error);
            throw error;
        }
    }

    /**
     * Search products with full-text search capabilities
     * @param {string} query - Search query
     * @param {Object} filters - Additional filters
     * @returns {Promise<Array>} Search results
     */
    async searchProducts(query, filters = {}) {
        const cacheKey = this._generateCacheKey('search', { query, filters });
        
        if (this.queryCache.has(cacheKey)) {
            return this.queryCache.get(cacheKey);
        }

        try {
            const db = await this.dbManager.getDatabase();
            const transaction = db.transaction(['products'], 'readonly');
            const store = transaction.objectStore('products');
            
            const results = [];
            const searchTerms = query.toLowerCase().split(' ');

            return new Promise((resolve, reject) => {
                const request = store.openCursor();

                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    
                    if (cursor) {
                        const product = cursor.value;
                        const searchText = `${product.title} ${product.description} ${product.asin}`.toLowerCase();
                        
                        // Check if all search terms are present
                        const matches = searchTerms.every(term => searchText.includes(term));
                        
                        if (matches && this._matchesFilter(product, filters)) {
                            results.push({
                                ...product,
                                relevanceScore: this._calculateRelevance(product, searchTerms)
                            });
                        }
                        
                        cursor.continue();
                    } else {
                        // Sort by relevance score
                        results.sort((a, b) => b.relevanceScore - a.relevanceScore);
                        
                        this._cacheResult(cacheKey, results);
                        resolve(results);
                    }
                };

                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error in searchProducts:', error);
            throw error;
        }
    }

    /**
     * Get top selling products for a specific timeframe
     * @param {string} timeframe - 'today', 'week', 'month', 'year'
     * @param {number} limit - Number of top products to return
     * @returns {Promise<Array>} Top selling products
     */
    async getTopSellingProducts(timeframe = 'month', limit = 100) {
        const cacheKey = this._generateCacheKey('topSelling', { timeframe, limit });
        
        if (this.queryCache.has(cacheKey)) {
            return this.queryCache.get(cacheKey);
        }

        try {
            const dateRange = this._getDateRangeForTimeframe(timeframe);
            const salesData = await this.getSalesDataByDateRange(dateRange.start, dateRange.end);
            
            // Aggregate sales by ASIN
            const salesByAsin = new Map();
            
            salesData.forEach(sale => {
                const current = salesByAsin.get(sale.asin) || { quantity: 0, revenue: 0 };
                current.quantity += sale.quantity;
                current.revenue += sale.revenue;
                salesByAsin.set(sale.asin, current);
            });

            // Convert to array and sort by quantity
            const topProducts = Array.from(salesByAsin.entries())
                .map(([asin, data]) => ({ asin, ...data }))
                .sort((a, b) => b.quantity - a.quantity)
                .slice(0, limit);

            this._cacheResult(cacheKey, topProducts);
            return topProducts;
        } catch (error) {
            console.error('Error in getTopSellingProducts:', error);
            throw error;
        }
    }

    /**
     * Stream data provider for virtual scrolling
     * @param {Object} config - Configuration for streaming
     * @returns {AsyncGenerator} Data stream
     */
    async* streamData(config) {
        const { type, filter, batchSize = 1000 } = config;
        let cursor = null;
        let hasMore = true;

        while (hasMore) {
            let batch;
            
            switch (type) {
                case 'products':
                    batch = await this.getProductsPaginated(filter, batchSize, cursor);
                    break;
                case 'sales':
                    batch = await this.getSalesDataByDateRange(filter.startDate, filter.endDate);
                    hasMore = false; // Sales data is typically smaller
                    break;
                default:
                    throw new Error(`Unsupported stream type: ${type}`);
            }

            yield batch.data || batch;
            
            cursor = batch.nextCursor;
            hasMore = batch.hasMore;
        }
    }

    /**
     * Clear query cache
     */
    clearCache() {
        this.queryCache.clear();
    }

    /**
     * Get cache statistics
     * @returns {Object} Cache statistics
     */
    getCacheStats() {
        return {
            size: this.queryCache.size,
            maxSize: this.maxCacheSize,
            hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0
        };
    }

    // Private helper methods
    _generateCacheKey(type, params) {
        return `${type}:${JSON.stringify(params)}`;
    }

    _cacheResult(key, result) {
        if (this.queryCache.size >= this.maxCacheSize) {
            // Remove oldest entry
            const firstKey = this.queryCache.keys().next().value;
            this.queryCache.delete(firstKey);
        }
        
        this.queryCache.set(key, result);
    }

    _matchesFilter(item, filter) {
        for (const [key, value] of Object.entries(filter)) {
            if (key === 'dateRange' || key === 'marketplace' || key === 'category') {
                continue; // Already handled by index
            }
            
            if (item[key] !== value) {
                return false;
            }
        }
        return true;
    }

    _calculateRelevance(product, searchTerms) {
        let score = 0;
        const title = product.title.toLowerCase();
        const description = product.description.toLowerCase();
        
        searchTerms.forEach(term => {
            if (title.includes(term)) score += 3;
            if (description.includes(term)) score += 1;
            if (product.asin.toLowerCase().includes(term)) score += 5;
        });
        
        return score;
    }

    _getDateRangeForTimeframe(timeframe) {
        const now = new Date();
        const start = new Date();
        
        switch (timeframe) {
            case 'today':
                start.setHours(0, 0, 0, 0);
                break;
            case 'week':
                start.setDate(now.getDate() - 7);
                break;
            case 'month':
                start.setMonth(now.getMonth() - 1);
                break;
            case 'year':
                start.setFullYear(now.getFullYear() - 1);
                break;
            default:
                start.setMonth(now.getMonth() - 1);
        }
        
        return { start, end: now };
    }
}

// Make QueryManager available globally
window.QueryManager = QueryManager;
