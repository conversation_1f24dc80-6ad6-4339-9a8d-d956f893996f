/**
 * Chart Data Provider for Massive Dataset Operations
 * Generates chart datasets from IndexedDB instead of in-memory calculations
 * Optimized for handling 5M+ products and 20M+ sales records
 */

class ChartDataProvider {
    constructor(queryManager, dataCacheManager) {
        this.queryManager = queryManager;
        this.cacheManager = dataCacheManager;
        this.aggregationCache = new Map();
        this.maxCacheAge = 5 * 60 * 1000; // 5 minutes
    }

    /**
     * Get today vs previous years comparison data
     * @returns {Promise<Object>} Chart data for today vs previous years
     */
    async getTodayVsPreviousYearsData() {
        const cacheKey = 'todayVsPreviousYears';
        const cached = await this.cacheManager.get(cacheKey);
        
        if (cached && !this._isCacheExpired(cached.timestamp)) {
            return cached.data;
        }

        try {
            const today = new Date();
            const currentYear = today.getFullYear();
            const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

            // Get today's sales
            const todaySales = await this.queryManager.getSalesDataByDateRange(todayStart, todayEnd);
            const todayRevenue = this._calculateTotalRevenue(todaySales);

            // Get same date for previous years (up to 3 years back)
            const previousYearsData = [];
            
            for (let i = 1; i <= 3; i++) {
                const yearStart = new Date(currentYear - i, today.getMonth(), today.getDate());
                const yearEnd = new Date(yearStart.getTime() + 24 * 60 * 60 * 1000);
                
                const yearSales = await this.queryManager.getSalesDataByDateRange(yearStart, yearEnd);
                const yearRevenue = this._calculateTotalRevenue(yearSales);
                
                previousYearsData.push({
                    year: currentYear - i,
                    revenue: yearRevenue,
                    orders: yearSales.length
                });
            }

            const chartData = {
                today: {
                    revenue: todayRevenue,
                    orders: todaySales.length,
                    date: todayStart.toISOString().split('T')[0]
                },
                previousYears: previousYearsData,
                comparison: {
                    revenueGrowth: this._calculateGrowthRate(todayRevenue, previousYearsData[0]?.revenue || 0),
                    ordersGrowth: this._calculateGrowthRate(todaySales.length, previousYearsData[0]?.orders || 0)
                }
            };

            await this.cacheManager.set(cacheKey, chartData, this.maxCacheAge);
            return chartData;
        } catch (error) {
            console.error('Error generating today vs previous years data:', error);
            throw error;
        }
    }

    /**
     * Get monthly sales data for a specific year
     * @param {number} year - Year to get data for
     * @returns {Promise<Object>} Monthly sales chart data
     */
    async getMonthlySalesData(year = new Date().getFullYear()) {
        const cacheKey = `monthlySales_${year}`;
        const cached = await this.cacheManager.get(cacheKey);
        
        if (cached && !this._isCacheExpired(cached.timestamp)) {
            return cached.data;
        }

        try {
            const monthlyData = [];
            
            for (let month = 0; month < 12; month++) {
                const monthStart = new Date(year, month, 1);
                const monthEnd = new Date(year, month + 1, 0, 23, 59, 59);
                
                const monthSales = await this.queryManager.getSalesDataByDateRange(monthStart, monthEnd);
                
                const monthStats = {
                    month: month + 1,
                    monthName: monthStart.toLocaleString('default', { month: 'long' }),
                    revenue: this._calculateTotalRevenue(monthSales),
                    orders: monthSales.length,
                    units: this._calculateTotalUnits(monthSales),
                    averageOrderValue: monthSales.length > 0 ? 
                        this._calculateTotalRevenue(monthSales) / monthSales.length : 0
                };
                
                monthlyData.push(monthStats);
            }

            const chartData = {
                year,
                months: monthlyData,
                totals: {
                    revenue: monthlyData.reduce((sum, month) => sum + month.revenue, 0),
                    orders: monthlyData.reduce((sum, month) => sum + month.orders, 0),
                    units: monthlyData.reduce((sum, month) => sum + month.units, 0)
                },
                trends: this._calculateMonthlyTrends(monthlyData)
            };

            await this.cacheManager.set(cacheKey, chartData, this.maxCacheAge);
            return chartData;
        } catch (error) {
            console.error('Error generating monthly sales data:', error);
            throw error;
        }
    }

    /**
     * Get marketplace comparison data
     * @param {string} timeframe - 'week', 'month', 'quarter', 'year'
     * @returns {Promise<Object>} Marketplace comparison chart data
     */
    async getMarketplaceComparisonData(timeframe = 'month') {
        const cacheKey = `marketplaceComparison_${timeframe}`;
        const cached = await this.cacheManager.get(cacheKey);
        
        if (cached && !this._isCacheExpired(cached.timestamp)) {
            return cached.data;
        }

        try {
            const dateRange = this._getDateRangeForTimeframe(timeframe);
            const salesData = await this.queryManager.getSalesDataByDateRange(dateRange.start, dateRange.end);
            
            // Group sales by marketplace
            const marketplaceStats = new Map();
            
            salesData.forEach(sale => {
                const marketplace = sale.marketplace || 'Unknown';
                const current = marketplaceStats.get(marketplace) || {
                    revenue: 0,
                    orders: 0,
                    units: 0,
                    products: new Set()
                };
                
                current.revenue += sale.revenue || 0;
                current.orders += 1;
                current.units += sale.quantity || 0;
                current.products.add(sale.asin);
                
                marketplaceStats.set(marketplace, current);
            });

            // Convert to array format for charts
            const marketplaceData = Array.from(marketplaceStats.entries()).map(([marketplace, stats]) => ({
                marketplace,
                revenue: stats.revenue,
                orders: stats.orders,
                units: stats.units,
                uniqueProducts: stats.products.size,
                averageOrderValue: stats.orders > 0 ? stats.revenue / stats.orders : 0,
                marketShare: 0 // Will be calculated below
            }));

            // Calculate market share
            const totalRevenue = marketplaceData.reduce((sum, mp) => sum + mp.revenue, 0);
            marketplaceData.forEach(mp => {
                mp.marketShare = totalRevenue > 0 ? (mp.revenue / totalRevenue) * 100 : 0;
            });

            // Sort by revenue
            marketplaceData.sort((a, b) => b.revenue - a.revenue);

            const chartData = {
                timeframe,
                dateRange,
                marketplaces: marketplaceData,
                totals: {
                    revenue: totalRevenue,
                    orders: marketplaceData.reduce((sum, mp) => sum + mp.orders, 0),
                    units: marketplaceData.reduce((sum, mp) => sum + mp.units, 0),
                    marketplaces: marketplaceData.length
                }
            };

            await this.cacheManager.set(cacheKey, chartData, this.maxCacheAge);
            return chartData;
        } catch (error) {
            console.error('Error generating marketplace comparison data:', error);
            throw error;
        }
    }

    /**
     * Get performance trends data
     * @param {string} metric - 'revenue', 'orders', 'units', 'aov'
     * @param {string} period - 'daily', 'weekly', 'monthly'
     * @param {number} days - Number of days to look back
     * @returns {Promise<Object>} Performance trends chart data
     */
    async getPerformanceTrends(metric = 'revenue', period = 'daily', days = 30) {
        const cacheKey = `performanceTrends_${metric}_${period}_${days}`;
        const cached = await this.cacheManager.get(cacheKey);
        
        if (cached && !this._isCacheExpired(cached.timestamp)) {
            return cached.data;
        }

        try {
            const endDate = new Date();
            const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));
            
            const salesData = await this.queryManager.getSalesDataByDateRange(startDate, endDate);
            
            // Group data by period
            const periodData = this._groupDataByPeriod(salesData, period);
            
            // Calculate trends
            const trendData = periodData.map(periodStats => {
                const value = this._calculateMetricValue(periodStats, metric);
                return {
                    date: periodStats.date,
                    value,
                    orders: periodStats.orders,
                    revenue: periodStats.revenue,
                    units: periodStats.units
                };
            });

            // Calculate trend analysis
            const trendAnalysis = this._analyzeTrend(trendData.map(d => d.value));

            const chartData = {
                metric,
                period,
                days,
                data: trendData,
                analysis: {
                    trend: trendAnalysis.direction,
                    slope: trendAnalysis.slope,
                    correlation: trendAnalysis.correlation,
                    average: trendAnalysis.average,
                    volatility: trendAnalysis.volatility
                }
            };

            await this.cacheManager.set(cacheKey, chartData, this.maxCacheAge);
            return chartData;
        } catch (error) {
            console.error('Error generating performance trends data:', error);
            throw error;
        }
    }

    /**
     * Get real-time dashboard summary
     * @returns {Promise<Object>} Real-time summary data
     */
    async getRealTimeSummary() {
        const cacheKey = 'realTimeSummary';
        const cached = await this.cacheManager.get(cacheKey);
        
        // Use shorter cache for real-time data (1 minute)
        if (cached && !this._isCacheExpired(cached.timestamp, 60000)) {
            return cached.data;
        }

        try {
            const today = new Date();
            const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const yesterday = new Date(todayStart.getTime() - 24 * 60 * 60 * 1000);
            const weekAgo = new Date(todayStart.getTime() - 7 * 24 * 60 * 60 * 1000);

            // Get data for different periods
            const [todaySales, yesterdaySales, weekSales] = await Promise.all([
                this.queryManager.getSalesDataByDateRange(todayStart, today),
                this.queryManager.getSalesDataByDateRange(yesterday, todayStart),
                this.queryManager.getSalesDataByDateRange(weekAgo, today)
            ]);

            const summary = {
                today: {
                    revenue: this._calculateTotalRevenue(todaySales),
                    orders: todaySales.length,
                    units: this._calculateTotalUnits(todaySales)
                },
                yesterday: {
                    revenue: this._calculateTotalRevenue(yesterdaySales),
                    orders: yesterdaySales.length,
                    units: this._calculateTotalUnits(yesterdaySales)
                },
                week: {
                    revenue: this._calculateTotalRevenue(weekSales),
                    orders: weekSales.length,
                    units: this._calculateTotalUnits(weekSales)
                },
                changes: {
                    revenueChange: this._calculatePercentageChange(
                        this._calculateTotalRevenue(todaySales),
                        this._calculateTotalRevenue(yesterdaySales)
                    ),
                    ordersChange: this._calculatePercentageChange(
                        todaySales.length,
                        yesterdaySales.length
                    )
                },
                lastUpdated: new Date().toISOString()
            };

            await this.cacheManager.set(cacheKey, summary, 60000); // 1 minute cache
            return summary;
        } catch (error) {
            console.error('Error generating real-time summary:', error);
            throw error;
        }
    }

    /**
     * Clear all chart data cache
     */
    clearCache() {
        this.aggregationCache.clear();
        this.cacheManager.clear();
    }

    // Private helper methods
    _calculateTotalRevenue(salesData) {
        return salesData.reduce((total, sale) => total + (sale.revenue || 0), 0);
    }

    _calculateTotalUnits(salesData) {
        return salesData.reduce((total, sale) => total + (sale.quantity || 0), 0);
    }

    _calculateGrowthRate(current, previous) {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
    }

    _calculatePercentageChange(current, previous) {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
    }

    _calculateMonthlyTrends(monthlyData) {
        const revenues = monthlyData.map(m => m.revenue);
        const orders = monthlyData.map(m => m.orders);
        
        return {
            revenue: this._analyzeTrend(revenues),
            orders: this._analyzeTrend(orders)
        };
    }

    _analyzeTrend(values) {
        if (values.length < 2) return { direction: 'stable', slope: 0 };
        
        // Simple linear regression
        const n = values.length;
        const x = Array.from({ length: n }, (_, i) => i);
        const sumX = x.reduce((a, b) => a + b, 0);
        const sumY = values.reduce((a, b) => a + b, 0);
        const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
        const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const average = sumY / n;
        
        // Calculate volatility (standard deviation)
        const variance = values.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / n;
        const volatility = Math.sqrt(variance);
        
        return {
            direction: slope > 0.1 ? 'increasing' : slope < -0.1 ? 'decreasing' : 'stable',
            slope,
            average,
            volatility,
            correlation: this._calculateCorrelation(x, values)
        };
    }

    _calculateCorrelation(x, y) {
        const n = x.length;
        const sumX = x.reduce((a, b) => a + b, 0);
        const sumY = y.reduce((a, b) => a + b, 0);
        const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
        const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
        const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);
        
        const numerator = n * sumXY - sumX * sumY;
        const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
        
        return denominator === 0 ? 0 : numerator / denominator;
    }

    _groupDataByPeriod(salesData, period) {
        const groups = new Map();
        
        salesData.forEach(sale => {
            const date = new Date(sale.date);
            let key;
            
            switch (period) {
                case 'daily':
                    key = date.toISOString().split('T')[0];
                    break;
                case 'weekly':
                    const weekStart = new Date(date);
                    weekStart.setDate(date.getDate() - date.getDay());
                    key = weekStart.toISOString().split('T')[0];
                    break;
                case 'monthly':
                    key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                    break;
                default:
                    key = date.toISOString().split('T')[0];
            }
            
            if (!groups.has(key)) {
                groups.set(key, {
                    date: key,
                    revenue: 0,
                    orders: 0,
                    units: 0,
                    sales: []
                });
            }
            
            const group = groups.get(key);
            group.revenue += sale.revenue || 0;
            group.orders += 1;
            group.units += sale.quantity || 0;
            group.sales.push(sale);
        });
        
        return Array.from(groups.values()).sort((a, b) => a.date.localeCompare(b.date));
    }

    _calculateMetricValue(periodStats, metric) {
        switch (metric) {
            case 'revenue':
                return periodStats.revenue;
            case 'orders':
                return periodStats.orders;
            case 'units':
                return periodStats.units;
            case 'aov':
                return periodStats.orders > 0 ? periodStats.revenue / periodStats.orders : 0;
            default:
                return periodStats.revenue;
        }
    }

    _getDateRangeForTimeframe(timeframe) {
        const now = new Date();
        const start = new Date();
        
        switch (timeframe) {
            case 'week':
                start.setDate(now.getDate() - 7);
                break;
            case 'month':
                start.setMonth(now.getMonth() - 1);
                break;
            case 'quarter':
                start.setMonth(now.getMonth() - 3);
                break;
            case 'year':
                start.setFullYear(now.getFullYear() - 1);
                break;
            default:
                start.setMonth(now.getMonth() - 1);
        }
        
        return { start, end: now };
    }

    _isCacheExpired(timestamp, maxAge = this.maxCacheAge) {
        return Date.now() - timestamp > maxAge;
    }
}

// Make ChartDataProvider available globally
window.ChartDataProvider = ChartDataProvider;
